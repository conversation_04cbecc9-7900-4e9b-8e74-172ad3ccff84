FROM python:3.12

# download and install dependencies for fast-service
WORKDIR /workspace/fast-service
COPY .cache/fast-service/requirements.txt requirements.txt
RUN pip install -r requirements.txt

# install dependencies for application
WORKDIR /workspace/applications
COPY requirements.txt requirements.txt
RUN pip install -r requirements.txt

# install fast-service
WORKDIR /workspace/fast-service
COPY .cache/fast-service/src src
COPY .cache/fast-service/pyproject.toml pyproject.toml
COPY .cache/fast-service/setup.py setup.py
RUN pip install -e .

# copy necessary code to docker image
WORKDIR /workspace/applications
COPY src src
COPY test test
COPY benchmark benchmark
COPY scripts scripts
COPY config config

# add src to PYTHONPATH
ENV PYTHONPATH=/workspace/applications/src

# set workdir and default command
WORKDIR /workspace/applications/scripts
CMD ["python", "launch_services.py"]