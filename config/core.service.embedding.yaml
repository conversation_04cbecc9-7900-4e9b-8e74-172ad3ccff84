service_name: embedding
scheduling_policy: round_robin
default_model: "BAAI/bge-reranker-large"

instances:
  # # OpenAI-compatible remote embedding service
  # - name: "vllm:BAAI/bge-reranker-large"
  #   location: "localhost:7000"
  #   model: "BAAI/bge-reranker-large"

  # Local embedding service using sentence-transformers
  - name: "local:BAAI/bge-reranker-large"
    location: "cuda:0"
    model: "BAAI/bge-reranker-large"
