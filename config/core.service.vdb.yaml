# VectorDB Service Configuration

# Default collection settings
default_collection: "default_collection"
default_dim: 1024
metric_type: "L2"

# VectorDB instances
instances:
  # - name: "milvus:remote"
  #   db_type: "milvus"
  #   location: "localhost:19530"  # Default Milvus server address
  #   collection_name: "default_collection"
  #   dim: 1024
  #   metric_type: "L2"
  #   index_params:
  #     index_type: "IVF_FLAT"
  #     params:
  #       nlist: 128
  #   search_params:
  #     nprobe: 10
  #   enabled: true

  # - name: "milvus:lite"
  #   db_type: "milvus_lite"
  #   location: "./.cache/milvus_lite.db"
  #   collection_name: "default_collection"
  #   dim: 1024
  #   metric_type: "L2"
  #   index_params:
  #     index_type: "IVF_FLAT"
  #     params:
  #       nlist: 128
  #   search_params:
  #     nprobe: 10
  #   enabled: true

  - name: "simple"
    db_type: "simple"
    collection_name: "simple"
    dim: 1024
    metric_type: "L2"
    index_params:
      index_type: "simple"
    search_params: {}
    enabled: true

# Global settings
max_retries: 3
timeout: 30.0
pool_size: 10
