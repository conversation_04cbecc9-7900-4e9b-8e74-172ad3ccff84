#!/bin/bash
# Enhanced test runner for lego_agent with improved testing framework

set -eo pipefail  # Strict error handling

# Configure paths and clean previous runs
mkdir -p .cache/test-reports .cache/htmlcov .cache/performance
rm -f .coverage .coverage.*

# Install test dependencies if not already installed
echo "=== Installing Test Dependencies ==="
pip install -e ".[test]"

# Function to run tests with proper reporting
run_test_suite() {
    local test_type=$1
    local test_path=$2
    local extra_args=${3:-""}

    echo -e "\n=== Running ${test_type} Tests ==="
    pytest ${test_path} \
        --junitxml=.cache/test-reports/${test_type,,}-tests.xml \
        --cov-append \
        ${extra_args} \
        -v
}

# Run different test suites
echo "=== Starting Test Execution ==="

# Unit tests - fast, isolated component tests
run_test_suite "Unit" "tests/unit" "-m unit"

# Integration tests - component interaction tests
run_test_suite "Integration" "tests/integration" "-m integration"

# Functional tests - end-to-end workflow tests
run_test_suite "Functional" "tests/functional" "-m functional"

# Performance tests - benchmarks and performance validation
run_test_suite "Performance" "tests/performance" "-m performance --benchmark-only"

# Legacy agent tests for backward compatibility
if [ -d "tests/agents" ]; then
    run_test_suite "Legacy-Agents" "tests/agents"
fi

echo -e "\n=== Generating Reports ==="
coverage html --directory=.cache/htmlcov
coverage report --show-missing

echo -e "\n=== Quality Metrics ==="
echo "- Coverage report: file://$(pwd)/.cache/htmlcov/index.html"
echo "- JUnit reports: file://$(pwd)/.cache/test-reports/"
echo "- Performance reports: file://$(pwd)/.cache/performance/"

echo -e "\n=== Test Commands Reference ==="
echo "# Run specific test types:"
echo "  pytest -m unit                    # Unit tests only"
echo "  pytest -m integration             # Integration tests only"
echo "  pytest -m functional              # Functional tests only"
echo "  pytest -m performance             # Performance tests only"
echo ""
echo "# Run specific test files:"
echo "  pytest tests/unit/test_services.py -v"
echo "  pytest tests/functional/test_workflow_end_to_end.py -v"
echo ""
echo "# Run with coverage:"
echo "  pytest --cov=src --cov-report=html"
echo ""
echo "# Run performance benchmarks:"
echo "  pytest tests/performance/ --benchmark-only"
echo ""
echo "# Run async tests only:"
echo "  pytest -m async_test"
echo ""
echo "# Run excluding slow tests:"
echo "  pytest -m 'not slow'"
