"""
Transform module for processing LLM outputs.

This module provides utility functions for extracting and transforming
structured data from LLM responses, such as parsing XML tags.
"""

import re


def extract_xml(text: str, tag: str) -> str:
    """
    Extracts the content of the specified XML tag from the given text.

    Used for parsing structured responses from LLMs where the output
    is formatted with XML tags to separate different components.

    Args:
        text (str): The text containing the XML.
        tag (str): The XML tag to extract content from.

    Returns:
        str: The content of the specified XML tag, or an empty string if the tag is not found.
    """
    match = re.search(f"<{tag}>(.*?)</{tag}>", text, re.DOTALL)
    return match.group(1) if match else ""


def extract_json(text: str, tag: str) -> str:
    """
    Extracts the content of the specified JSON tag from the given text. Used for parsing structured responses

    Args:
        text (str): The text containing the JSON.
        tag (str): The JSON tag to extract content from.

    Returns:
        str: The content of the specified JSON tag, or an empty string if the tag is not found.
    """
    match = re.search(f'"{tag}":(.*?),', text)
    return match.group(1) if match else ""
