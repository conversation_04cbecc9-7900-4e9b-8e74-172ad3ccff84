"""
LLM task module for making LLM API calls.

This module provides standardized functions for making synchronous and asynchronous
calls to language models through the service manager infrastructure.
"""

from typing import cast, Optional
from lego_agent.core.registry_setup import get_service_manager
from lego_agent.core.service.llm import LLMServiceRequest, LLMServiceResponse
from lego_agent.core.schema.io import TaskRequest, TaskResponse


class LLMTaskRequest(TaskRequest):
    task_name: str = "llm_call"
    prompt: str
    system_prompt: str = ""
    model: Optional[str] = None


class LLMTaskResponse(TaskResponse):
    task_name: str = "llm_call"
    response: Optional[str] = None


def llm_call(request: LLMTaskRequest) -> LLMTaskResponse:
    """
    Calls the model with the given prompt and returns the response.

    Args:
        request (LLMTaskRequest): The request to send to the model.

    Returns:
        LLMTaskResponse: The response from the language model.
    """
    # Get service manager from centralized registry
    service_manager = get_service_manager()
    llm = service_manager.get_service("llm")

    messages = [{"role": "user", "content": request.prompt}]
    if request.system_prompt:
        messages.insert(0, {"role": "system", "content": request.system_prompt})
    llm_request = LLMServiceRequest(
        service_name="llm",
        operation_name="chat",
        raw_data={"messages": messages, "model": request.model},
    )
    response = cast(LLMServiceResponse, llm.serve(llm_request, request.model))
    response_data = response.raw_data.get("response")
    if response_data is None:
        raise ValueError("Invalid response from LLM service")
    return LLMTaskResponse(response=response_data.choices[0].message.content)


async def llm_call_async(request: LLMTaskRequest) -> LLMTaskResponse:
    """
    Calls the model with the given prompt and returns the response asynchronously.

    Args:
        request (LLMTaskRequest): The request to send to the model.

    Returns:
        LLMTaskResponse: The response from the language model.
    """
    # Get service manager from centralized registry
    service_manager = get_service_manager()
    llm = service_manager.get_service("llm")

    messages = [{"role": "user", "content": request.prompt}]
    if request.system_prompt:
        messages.insert(0, {"role": "system", "content": request.system_prompt})
    llm_request = LLMServiceRequest(
        service_name="llm",
        operation_name="chat",
        raw_data={"messages": messages, "model": request.model},
    )
    response = cast(
        LLMServiceResponse, await llm.serve_async(llm_request, request.model)
    )
    response_data = response.raw_data.get("response")
    if response_data is None:
        raise ValueError("Invalid response from LLM service")
    return LLMTaskResponse(response=response_data.choices[0].message.content)
