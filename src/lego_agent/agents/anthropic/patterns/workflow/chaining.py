"""
Chaining module for sequential LLM processing.

This module provides functions for chaining multiple LLM calls together,
where the output of one call becomes the input to the next call.
"""

from optparse import Option
from typing import List, Optional
from pydantic import Field
from lego_agent.core.config import config_manager
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    TaskRequest,
    TaskResponse,
    LLMTaskRequest,
    LLMTaskResponse,
    llm_call,
    llm_call_async,
)


class ChainWorkflowRequest(TaskRequest):
    """Request model for chain workflow.

    Attributes:
        input: The initial input to start the chain
        prompts: List of prompts to process sequentially
    """

    task_name: str = "chain_workflow"

    input: str = Field(..., description="The initial input to start the chain")
    prompts: List[str] = Field(
        ..., description="List of prompts to process sequentially"
    )
    model: Optional[str] = None


class ChainWorkflowResponse(TaskResponse):
    """Response model for chain workflow.

    Attributes:
        output: The final output after processing all prompts in the chain
        steps: List of intermediate outputs from each step in the chain
    """

    task_name: str = "chain_workflow"

    output: str = Field(
        ..., description="The final output after processing all prompts"
    )
    steps: List[str] = Field(
        default_factory=list, description="Intermediate outputs from each step"
    )


def chain(request: ChainWorkflowRequest) -> ChainWorkflowResponse:
    """Chain multiple LLM calls sequentially, passing results between steps."""
    # Use provided model or get from config
    model = request.model or config_manager.get_config_value("anthropic.chain", "model")

    steps = []
    result = request.input
    for i, prompt in enumerate(request.prompts, 1):
        print(f"\nStep {i}:")
        step_request = LLMTaskRequest(prompt=f"{prompt}\nInput: {result}", model=model)
        result = llm_call(step_request).response
        steps.append(result)
        print(result)
    return ChainWorkflowResponse(output=result, steps=steps)


async def chain_async(request: ChainWorkflowRequest) -> ChainWorkflowResponse:
    """Chain multiple LLM calls sequentially, passing results between steps asynchronously."""
    # Use provided model or get from config
    model = request.model or config_manager.get_config_value("anthropic.chain", "model")

    steps = []
    result = request.input
    for i, prompt in enumerate(request.prompts, 1):
        print(f"\nStep {i}:")
        step_request = LLMTaskRequest(prompt=f"{prompt}\nInput: {result}", model=model)
        response = await llm_call_async(step_request)
        result = response.response
        steps.append(result)
        print(result)
    return ChainWorkflowResponse(output=result, steps=steps)
