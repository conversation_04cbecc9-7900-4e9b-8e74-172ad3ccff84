"""
Looping module for iterative LLM refinement.

This module provides functions for iteratively generating and refining solutions
through repeated LLM calls with evaluation feedback until requirements are met.
"""

from typing import Dict, List, Optional, Any, Literal
from enum import Enum
from pydantic import Field, BaseModel
from lego_agent.core.config import config_manager
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    TaskRequest,
    TaskResponse,
    LLMTaskRequest,
    LLMTaskResponse,
    llm_call,
    llm_call_async,
)
from lego_agent.agents.anthropic.patterns.function.transform import extract_xml


class EvaluationResult(str, Enum):
    """Possible evaluation results for a solution."""

    PASS = "PASS"
    NEEDS_IMPROVEMENT = "NEEDS_IMPROVEMENT"
    FAIL = "FAIL"


class GenerationStep(BaseModel):
    """A single step in the generation process."""

    thoughts: str = Field(..., description="The model's reasoning process")
    result: str = Field(..., description="The generated solution")
    feedback: Optional[str] = Field(
        default=None, description="Feedback from evaluation"
    )
    evaluation: Optional[EvaluationResult] = Field(
        default=None, description="Evaluation result"
    )


class LoopWorkflowRequest(TaskRequest):
    """Request model for loop workflow.

    Attributes:
        task: The task to solve
        max_iterations: Maximum number of iterations to try
        model: Optional model override
        show_thoughts: Whether to show thoughts in output
        return_intermediate: Whether to return intermediate results
    """

    task_name: str = "loop_workflow"

    task: str = Field(..., description="The task to solve")
    max_iterations: int = Field(
        default=3, description="Maximum number of iterations to try"
    )
    model: Optional[str] = Field(default=None, description="Optional model override")
    show_thoughts: bool = Field(
        default=True, description="Whether to show thoughts in output"
    )
    return_intermediate: bool = Field(
        default=False, description="Whether to return intermediate results"
    )


class LoopWorkflowResponse(TaskResponse):
    """Response model for loop workflow.

    Attributes:
        result: The final result after all iterations
        steps: List of all generation steps
        success: Whether the loop completed successfully
        iterations: Number of iterations performed
    """

    task_name: str = "loop_workflow"

    result: str = Field(..., description="The final result after all iterations")
    steps: List[GenerationStep] = Field(
        default_factory=list, description="List of all generation steps"
    )
    success: bool = Field(..., description="Whether the loop completed successfully")
    iterations: int = Field(..., description="Number of iterations performed")


def get_generator_prompt():
    generator_prompt = """
        Your goal is to complete the task based on <user input>. If there is feedback
        from previous generations, you should reflect on it to improve your solution.

        Output your answer concisely in the following format:

        <think>
        [Your understanding of the task and feedback and how you plan to improve]
        </think>

        <response>
        [Your solution implementation]
        </response>
        """
    return generator_prompt


def get_evaluator_promt(task: str, solution: str):
    evaluator_prompt = """Evaluate this following solution for:
        1. Correctness
        2. Completeness
        3. Quality

        You should be evaluating only and not attempting to solve the task.
        Only output "PASS" if all criteria are met and you have no further suggestions for improvements.
        Output your evaluation concisely in the following format:

        <evaluation>
        [PASS, NEEDS_IMPROVEMENT, or FAIL]
        </evaluation>

        <feedback>
        [What needs improvement and why.]
        </feedback>

        Task: {task}
        Solution: {solution}
        """
    return evaluator_prompt.format(task=task, solution=solution)


def _generate(
    task: str,
    context: str = "",
    model: Optional[str] = None,
    show_thoughts: bool = True,
) -> tuple[str, str]:
    """Generate a solution based on the task and context.

    Args:
        task: The task to generate a solution for
        context: Optional context from previous attempts
        model: Optional model override
        show_thoughts: Whether to show thoughts in output

    Returns:
        Tuple of (thoughts, result)
    """
    generator_prompt = get_generator_prompt()

    full_prompt = (
        f"{generator_prompt}\n{context}\nTask: {task}"
        if context
        else f"{generator_prompt}\nTask: {task}"
    )

    llm_request = LLMTaskRequest(prompt=full_prompt, model=model)
    response = llm_call(llm_request).response
    thoughts = extract_xml(response, "think")
    result = extract_xml(response, "response")

    if show_thoughts:
        print("\n=== GENERATION START ===")
        print(f"Thoughts:\n{thoughts}\n")
        print(f"Generated:\n{result}")
        print("=== GENERATION END ===\n")

    return thoughts, result


def _evaluate(
    solution: str, task: str, model: Optional[str] = None
) -> tuple[EvaluationResult, str]:
    """Evaluate a solution against requirements.

    Args:
        solution: The solution to evaluate
        task: The original task
        model: Optional model override

    Returns:
        Tuple of (evaluation result, feedback)
    """
    full_prompt = get_evaluator_promt(task=task, solution=solution)
    llm_request = LLMTaskRequest(prompt=full_prompt, model=model)
    response = llm_call(llm_request).response

    eval_text = extract_xml(response, "evaluation").strip().upper()
    # Handle both [PASS] and PASS formats
    if eval_text.startswith("[") and eval_text.endswith("]"):
        eval_text = eval_text[1:-1].strip()

    try:
        evaluation = EvaluationResult(eval_text)
    except ValueError as e:
        print(
            f"Warning: Invalid evaluation result '{eval_text}'. Defaulting to NEEDS_IMPROVEMENT."
        )
        evaluation = EvaluationResult.NEEDS_IMPROVEMENT

    feedback = extract_xml(response, "feedback")

    print(f"\nEvaluation: {evaluation}")
    print(f"Feedback: {feedback}\n")

    return evaluation, feedback


def loop(request: LoopWorkflowRequest) -> LoopWorkflowResponse:
    """Keep generating and evaluating until requirements are met.

    Args:
        request: The loop workflow request

    Returns:
        LoopWorkflowResponse containing the final result and generation steps
    """
    # Use provided values or get from config
    model = request.model or config_manager.get_config_value("anthropic.loop", "model")
    show_thoughts = config_manager.get_config_value(
        "anthropic.loop", "show_thoughts", request.show_thoughts
    )

    steps: List[GenerationStep] = []
    context = ""

    for iteration in range(1, request.max_iterations + 1):
        print(f"\n=== ITERATION {iteration} ===")

        # Generate a solution
        thoughts, result = _generate(
            request.task, context, model=model, show_thoughts=show_thoughts
        )

        # Evaluate the solution
        evaluation, feedback = _evaluate(result, request.task, model=model)

        # Record the step
        step = GenerationStep(
            thoughts=thoughts, result=result, feedback=feedback, evaluation=evaluation
        )
        steps.append(step)

        # Check if we're done
        if evaluation == EvaluationResult.PASS:
            return LoopWorkflowResponse(
                result=result,
                steps=steps if request.return_intermediate else [step],
                success=True,
                iterations=iteration,
            )

        # Prepare context for next iteration
        context = "\n".join(
            [
                "Previous attempts:",
                *[f"- {step.result}" for step in steps],
                f"\nFeedback: {feedback}",
            ]
        )

    # If we get here, we've used all iterations without passing
    return LoopWorkflowResponse(
        result=steps[-1].result,
        steps=steps if request.return_intermediate else [steps[-1]],
        success=False,
        iterations=request.max_iterations,
    )


async def _generate_async(
    task: str,
    context: str = "",
    model: Optional[str] = None,
    show_thoughts: bool = True,
) -> tuple[str, str]:
    """Generate a solution asynchronously based on the task and context.

    Args:
        task: The task to generate a solution for
        context: Optional context from previous attempts
        model: Optional model override
        show_thoughts: Whether to show thoughts in output

    Returns:
        Tuple of (thoughts, result)
    """
    generator_prompt = get_generator_prompt()

    full_prompt = (
        f"{generator_prompt}\n{context}\nTask: {task}"
        if context
        else f"{generator_prompt}\nTask: {task}"
    )

    llm_request = LLMTaskRequest(prompt=full_prompt, model=model)
    response = (await llm_call_async(llm_request)).response
    thoughts = extract_xml(response, "think")
    result = extract_xml(response, "response")

    if show_thoughts:
        print("\n=== GENERATION START ===")
        print(f"Thoughts:\n{thoughts}\n")
        print(f"Generated:\n{result}")
        print("=== GENERATION END ===\n")

    return thoughts, result


async def _evaluate_async(
    solution: str, task: str, model: Optional[str] = None
) -> tuple[EvaluationResult, str]:
    """Evaluate a solution against requirements asynchronously.

    Args:
        solution: The solution to evaluate
        task: The original task
        model: Optional model override

    Returns:
        Tuple of (evaluation result, feedback)
    """
    full_prompt = get_evaluator_promt(task=task, solution=solution)
    llm_request = LLMTaskRequest(prompt=full_prompt, model=model)
    response = (await llm_call_async(llm_request)).response

    eval_text = extract_xml(response, "evaluation").strip().upper()
    # Handle both [PASS] and PASS formats
    if eval_text.startswith("[") and eval_text.endswith("]"):
        eval_text = eval_text[1:-1].strip()

    try:
        evaluation = EvaluationResult(eval_text)
    except ValueError as e:
        print(
            f"Warning: Invalid evaluation result '{eval_text}'. Defaulting to NEEDS_IMPROVEMENT."
        )
        evaluation = EvaluationResult.NEEDS_IMPROVEMENT

    feedback = extract_xml(response, "feedback")

    print(f"\nEvaluation: {evaluation}")
    print(f"Feedback: {feedback}\n")

    return evaluation, feedback


async def loop_async(request: LoopWorkflowRequest) -> LoopWorkflowResponse:
    """Keep generating and evaluating asynchronously until requirements are met.

    Args:
        request: The loop workflow request

    Returns:
        LoopWorkflowResponse containing the final result and generation steps
    """
    # Use provided values or get from config
    model = request.model or config_manager.get_config_value("anthropic.loop", "model")
    show_thoughts = config_manager.get_config_value(
        "anthropic.loop", "show_thoughts", request.show_thoughts
    )

    steps: List[GenerationStep] = []
    context = ""

    for iteration in range(1, request.max_iterations + 1):
        print(f"\n=== ITERATION {iteration} ===")

        # Generate a solution asynchronously
        thoughts, result = await _generate_async(
            request.task, context, model=model, show_thoughts=show_thoughts
        )

        # Evaluate the solution asynchronously
        evaluation, feedback = await _evaluate_async(result, request.task, model=model)

        # Record the step
        step = GenerationStep(
            thoughts=thoughts, result=result, feedback=feedback, evaluation=evaluation
        )
        steps.append(step)

        # Check if we're done
        if evaluation == EvaluationResult.PASS:
            return LoopWorkflowResponse(
                result=result,
                steps=steps if request.return_intermediate else [step],
                success=True,
                iterations=iteration,
            )

        # Prepare context for next iteration
        context = "\n".join(
            [
                "Previous attempts:",
                *[f"- {step.result}" for step in steps],
                f"\nFeedback: {feedback}",
            ]
        )

    # If we get here, we've used all iterations without passing
    return LoopWorkflowResponse(
        result=steps[-1].result,
        steps=steps if request.return_intermediate else [steps[-1]],
        success=False,
        iterations=request.max_iterations,
    )
