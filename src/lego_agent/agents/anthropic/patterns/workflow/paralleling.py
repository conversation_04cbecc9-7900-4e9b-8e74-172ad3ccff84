"""
Paralleling module for concurrent LLM processing.

This module provides functions for processing multiple inputs concurrently
using the same prompt, leveraging both thread-based and async-based parallelism.
"""

from concurrent.futures import ThreadPoolExecutor
import asyncio
from typing import List, Optional, Dict, Any
from pydantic import Field
from lego_agent.core.config import config_manager
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    TaskRequest,
    TaskResponse,
    LLMTaskRequest,
    LLMTaskResponse,
    llm_call,
    llm_call_async,
)


class ParallelWorkflowRequest(TaskRequest):
    """Request model for parallel workflow.

    Attributes:
        prompt: The prompt to use for all inputs
        inputs: List of inputs to process in parallel
        n_workers: Number of parallel workers to use
        model: Optional model override
    """

    task_name: str = "parallel_workflow"

    prompt: str = Field(..., description="The prompt to use for all inputs")
    inputs: List[str] = Field(..., description="List of inputs to process in parallel")
    n_workers: int = Field(default=3, description="Number of parallel workers to use")
    model: Optional[str] = Field(default=None, description="Optional model override")


class ParallelWorkflowResponse(TaskResponse):
    """Response model for parallel workflow.

    Attributes:
        outputs: List of outputs corresponding to each input
        metadata: Additional metadata about the parallel execution
    """

    task_name: str = "parallel_workflow"

    outputs: List[str] = Field(..., description="List of outputs for each input")
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata about the parallel execution",
    )


def parallel(request: ParallelWorkflowRequest) -> ParallelWorkflowResponse:
    """Process multiple inputs concurrently with the same prompt.

    Args:
        request: The parallel processing request

    Returns:
        ParallelWorkflowResponse containing the outputs and metadata
    """
    # Use provided values or get from config
    n_workers = request.n_workers or config_manager.get_config_value(
        "anthropic.parallel", "n_workers", 3
    )
    model = request.model or config_manager.get_config_value(
        "anthropic.parallel", "model"
    )

    def process_input(input_text: str) -> str:
        """Process a single input with the prompt."""
        llm_request = LLMTaskRequest(
            prompt=f"{request.prompt}\nInput: {input_text}", model=model
        )
        return llm_call(llm_request).response or ""

    with ThreadPoolExecutor(max_workers=n_workers) as executor:
        outputs = list(executor.map(process_input, request.inputs))

    return ParallelWorkflowResponse(
        outputs=outputs,
        metadata={
            "n_workers": n_workers,
            "model": model,
            "n_inputs": len(request.inputs),
        },
    )


async def parallel_async(request: ParallelWorkflowRequest) -> ParallelWorkflowResponse:
    """Process multiple inputs concurrently with the same prompt asynchronously.

    Args:
        request: The parallel processing request

    Returns:
        ParallelWorkflowResponse containing the outputs and metadata
    """
    # Use provided model or get from config
    model = request.model or config_manager.get_config_value(
        "anthropic.parallel", "model"
    )
    n_workers = request.n_workers or config_manager.get_config_value(
        "anthropic.parallel", "n_workers", 3
    )

    async def process_input_async(input_text: str) -> str:
        """Process a single input with the prompt asynchronously."""
        llm_request = LLMTaskRequest(
            prompt=f"{request.prompt}\nInput: {input_text}", model=model
        )
        response = await llm_call_async(llm_request)
        return response.response or ""

    # Process inputs concurrently with semaphore to limit concurrency
    semaphore = asyncio.Semaphore(n_workers)

    async def process_with_semaphore(input_text: str) -> str:
        async with semaphore:
            return await process_input_async(input_text)

    tasks = [process_with_semaphore(input_text) for input_text in request.inputs]
    outputs = await asyncio.gather(*tasks)

    return ParallelWorkflowResponse(
        outputs=outputs,
        metadata={
            "n_workers": n_workers,
            "model": model,
            "n_inputs": len(request.inputs),
        },
    )
