"""
Planner-Worker module for parallel LLM task processing.

This module implements a planner-worker pattern where a planner <PERSON><PERSON> breaks down a task
into subtasks, which are then processed in parallel by worker LLMs.
"""

from typing import Dict, List, Optional, Any
from pydantic import Field, BaseModel
from lego_agent.core.config import config_manager
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    TaskRequest,
    TaskResponse,
    LLMTaskRequest,
    LLMTaskResponse,
    llm_call,
    llm_call_async,
)
from lego_agent.agents.anthropic.patterns.function.transform import extract_xml


class WorkerTask(BaseModel):
    """A single task to be processed by a worker.

    Attributes:
        type: The type/category of the task
        description: Detailed description of what the task should accomplish
    """

    type: str = Field(..., description="The type/category of the task")
    description: str = Field(..., description="Detailed description of the task")


class PlannerWorkersRequest(TaskRequest):
    """Request model for planner-workers workflow.

    Attributes:
        task: The main task to be broken down and processed
        max_subtasks: Maximum number of subtasks to create (default: 3)
        model: Optional model override for both planner and workers
    """

    task_name: str = "planner_workers"
    task: str = Field(..., description="The main task to be broken down and processed")
    max_subtasks: int = Field(
        default=3, ge=1, le=5, description="Maximum number of subtasks to create"
    )
    model: Optional[str] = Field(
        default=None, description="Optional model override for both planner and workers"
    )


class WorkerResult(BaseModel):
    """Result from a single worker task.

    Attributes:
        type: The type/category of the task
        description: Description of what the task was supposed to accomplish
        result: The generated result from the worker
    """

    type: str = Field(..., description="The type/category of the task")
    description: str = Field(..., description="Description of the task")
    result: str = Field(..., description="The generated result from the worker")


class PlannerWorkersResponse(TaskResponse):
    """Response model for planner-workers workflow.

    Attributes:
        analysis: The planner's analysis of the task
        worker_results: List of results from all worker tasks
    """

    task_name: str = "planner_workers"
    analysis: str = Field(..., description="The planner's analysis of the task")
    worker_results: List[WorkerResult] = Field(
        default_factory=list, description="List of results from all worker tasks"
    )


def _get_planner_prompt(task: str, max_subtasks: int) -> str:
    """Generate the prompt for the planner LLM.

    Args:
        task: The main task to analyze
        max_subtasks: Maximum number of subtasks to create

    Returns:
        Formatted prompt string
    """
    return f"""You are an expert task planner. Your job is to break down the following task into {max_subtasks} subtasks that can be worked on in parallel by different team members.

Task: {task}

Please provide:
1. A brief analysis of the task and how it can be divided.
2. A list of {max_subtasks} subtasks with clear descriptions.

Format your response as follows:

<analysis>
[Your analysis of the task and approach to dividing it]
</analysis>

<tasks>
<task>
<type>[task type/category]</type>
<description>[detailed description of the subtask]</description>
</task>
[Additional tasks...]
</tasks>
"""


def _get_worker_prompt(
    original_task: str, task_type: str, task_description: str
) -> str:
    """Generate the prompt for a worker LLM.

    Args:
        original_task: The original task
        task_type: Type of the subtask
        task_description: Description of the subtask

    Returns:
        Formatted prompt string
    """
    return f"""You are an expert worker assigned to complete a specific subtask.

Original Task: {original_task}

Your Subtask Type: {task_type}
Your Subtask Description: {task_description}

Please provide a detailed and high-quality response to this subtask. Be thorough and specific in your answer."""


def _parse_tasks(tasks_xml: str) -> List[WorkerTask]:
    """Parse XML tasks into a list of WorkerTask objects.

    Args:
        tasks_xml: XML string containing task definitions

    Returns:
        List of parsed WorkerTask objects

    Raises:
        ValueError: If the XML is malformed or missing required fields
    """
    tasks = []
    current_task = {}

    # If no tasks_xml was provided, return a default task
    if not tasks_xml or tasks_xml.strip() == "":
        print("No tasks XML provided, using default task")
        return [
            WorkerTask(
                type="default",
                description="Generate a product description for the given task",
            )
        ]

    try:
        for line in tasks_xml.split("\n"):
            line = line.strip()
            if not line:
                continue

            if line.startswith("<task>"):
                current_task = {}
            elif line.startswith("<type>"):
                current_task["type"] = line[6:-7].strip()
            elif line.startswith("<description>"):
                current_task["description"] = line[12:-13].strip()
            elif line.startswith("</task>"):
                if "description" in current_task or "type" in current_task:
                    if "type" not in current_task:
                        current_task["type"] = "default"
                    if "description" not in current_task:
                        current_task["description"] = "No description provided"
                    tasks.append(WorkerTask(**current_task))

        if not tasks:
            print("No valid tasks found in XML, using default task")
            return [
                WorkerTask(
                    type="default",
                    description="Generate a product description for the given task",
                )
            ]

        return tasks
    except Exception as e:
        print(f"Error parsing tasks: {str(e)}, using default task")
        return [
            WorkerTask(
                type="default",
                description="Generate a product description for the given task",
            )
        ]


def process(request: PlannerWorkersRequest) -> PlannerWorkersResponse:
    """Process a task using the planner-workers pattern.

    Args:
        request: The planner-workers workflow request

    Returns:
        PlannerWorkersResponse containing the analysis and worker results
    """
    # Use provided model or get from config
    model = request.model or config_manager.get_config_value(
        "anthropic.planner_workers", "model"
    )

    # Step 1: Get planner/orchestrator response
    planner_prompt = _get_planner_prompt(request.task, request.max_subtasks)
    planner_request = LLMTaskRequest(prompt=planner_prompt, model=model)
    planner_response = llm_call(planner_request).response

    # Parse planner response with better error handling
    try:
        analysis = extract_xml(planner_response, "analysis")
        if analysis:
            analysis = analysis.strip()
        tasks_xml = extract_xml(planner_response, "tasks")

        if not analysis:
            print("Warning: No analysis found in planner response")
            analysis = "No analysis provided by the planner agent"

        if not tasks_xml:
            print("Warning: No tasks found in planner response")
            # Provide a default task if none are found
            tasks_xml = """
            <tasks>
                <task>
                    <type>default</type>
                    <description>Generate a product description for the given task</description>
                </task>
            </tasks>
            """
    except Exception as e:
        print(f"Error parsing planner response: {str(e)}")
        analysis = "Error parsing planner response: " + str(e)
        tasks_xml = """
        <tasks>
            <task>
                <type>default</type>
                <description>Generate a product description for the given task</description>
            </task>
        </tasks>
        """

    tasks = _parse_tasks(tasks_xml)

    print("\n=== PLANNER ANALYSIS ===")
    print(analysis)

    # Step 2: Process each task in parallel (could be optimized with asyncio.gather)
    worker_results = []
    for task_info in tasks:
        worker_prompt = _get_worker_prompt(
            original_task=request.task,
            task_type=task_info.type,
            task_description=task_info.description,
        )

        worker_request = LLMTaskRequest(prompt=worker_prompt, model=model)

        try:
            worker_response = llm_call(worker_request).response
            worker_results.append(
                WorkerResult(
                    type=task_info.type,
                    description=task_info.description,
                    result=worker_response.strip(),
                )
            )
        except Exception as e:
            print(f"Error processing worker task: {str(e)}")
            worker_results.append(
                WorkerResult(
                    type=task_info.type,
                    description=task_info.description,
                    result=f"Error processing task: {str(e)}",
                )
            )

    return PlannerWorkersResponse(analysis=analysis, worker_results=worker_results)


async def process_async(request: PlannerWorkersRequest) -> PlannerWorkersResponse:
    """Process a task asynchronously using the planner-workers pattern.

    Args:
        request: The planner-workers workflow request

    Returns:
        PlannerWorkersResponse containing the analysis and worker results
    """
    # Use provided model or get from config
    model = request.model or config_manager.get_config_value(
        "anthropic.planner_workers", "model"
    )

    # Step 1: Get planner/orchestrator response
    planner_prompt = _get_planner_prompt(request.task, request.max_subtasks)
    planner_request = LLMTaskRequest(prompt=planner_prompt, model=model)
    planner_response = (await llm_call_async(planner_request)).response

    # Parse planner response with better error handling
    try:
        analysis = extract_xml(planner_response, "analysis")
        if analysis:
            analysis = analysis.strip()
        tasks_xml = extract_xml(planner_response, "tasks")

        if not analysis:
            print("Warning: No analysis found in planner response")
            analysis = "No analysis provided by the planner agent"

        if not tasks_xml:
            print("Warning: No tasks found in planner response")
            # Provide a default task if none are found
            tasks_xml = """
            <tasks>
                <task>
                    <type>default</type>
                    <description>Generate a product description for the given task</description>
                </task>
            </tasks>
            """
    except Exception as e:
        print(f"Error parsing planner response: {str(e)}")
        analysis = "Error parsing planner response: " + str(e)
        tasks_xml = """
        <tasks>
            <task>
                <type>default</type>
                <description>Generate a product description for the given task</description>
            </task>
        </tasks>
        """

    tasks = _parse_tasks(tasks_xml)

    print("\n=== PLANNER ANALYSIS ===")
    print(analysis)

    # Step 2: Process each task in parallel
    worker_tasks = []
    for task_info in tasks:
        worker_prompt = _get_worker_prompt(
            original_task=request.task,
            task_type=task_info.type,
            task_description=task_info.description,
        )

        worker_request = LLMTaskRequest(prompt=worker_prompt, model=model)

        worker_tasks.append(worker_request)

    # Process all worker tasks in parallel
    worker_responses = []
    for worker_request in worker_tasks:
        try:
            response = await llm_call_async(worker_request)
            worker_responses.append(response)
        except Exception as e:
            print(f"Error in async worker task: {str(e)}")
            worker_responses.append(None)

    # Process responses
    worker_results = []
    for i, response in enumerate(worker_responses):
        task_info = tasks[i]
        if response and response.response:
            worker_results.append(
                WorkerResult(
                    type=task_info.type,
                    description=task_info.description,
                    result=response.response.strip(),
                )
            )
        else:
            worker_results.append(
                WorkerResult(
                    type=task_info.type,
                    description=task_info.description,
                    result="Error: No response from worker",
                )
            )

    return PlannerWorkersResponse(analysis=analysis, worker_results=worker_results)
