"""
Routing module for content-based prompt selection.

This module provides functions for routing inputs to specialized prompts
based on content classification performed by an LLM.
"""

from typing import Dict, Optional, List
from pydantic import Field
from lego_agent.core.config import config_manager
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    TaskRequest,
    TaskResponse,
    LLMTaskRequest,
    LLMTaskResponse,
    llm_call,
    llm_call_async,
)
from lego_agent.agents.anthropic.patterns.function.transform import extract_xml


class RouteWorkflowRequest(TaskRequest):
    """Request model for route workflow.

    Attributes:
        input: The input to route
        routes: Dictionary mapping route keys to specialized prompts
        model: Optional model override
        show_reasoning: Whether to show reasoning in output
    """

    task_name: str = "route_workflow"

    input: str = Field(..., description="The input to route")
    routes: Dict[str, str] = Field(
        ..., description="Dictionary mapping route keys to specialized prompts"
    )
    model: Optional[str] = Field(default=None, description="Optional model override")
    show_reasoning: bool = Field(
        default=True, description="Whether to show reasoning in output"
    )


class RouteWorkflowResponse(TaskResponse):
    """Response model for route workflow.

    Attributes:
        output: The final output after processing with the selected prompt
        route_key: The key of the selected route
        reasoning: The reasoning behind the route selection
    """

    task_name: str = "route_workflow"

    output: str = Field(
        ..., description="The final output after processing with the selected prompt"
    )
    route_key: str = Field(..., description="The key of the selected route")
    reasoning: str = Field(..., description="The reasoning behind the route selection")


def route(request: RouteWorkflowRequest) -> RouteWorkflowResponse:
    """Route input to specialized prompt using content classification.

    Args:
        request: The routing request containing input, routes, and configuration

    Returns:
        RouteWorkflowResponse containing the output, selected route, and reasoning
    """
    # Use provided values or get from config
    model = request.model or config_manager.get_config_value("anthropic.route", "model")
    show_reasoning = (
        request.show_reasoning
        if request.show_reasoning is not None
        else config_manager.get_config_value("anthropic.route", "show_reasoning", True)
    )

    # First determine appropriate route using LLM with chain-of-thought
    print(f"\nAvailable routes: {list(request.routes.keys())}")
    selector_prompt = f"""
    Analyze the input and select the most appropriate support team from these options: {list(request.routes.keys())}
    First explain your reasoning, then provide your selection in this XML format:

    <reasoning>
    Brief explanation of why this ticket should be routed to a specific team.
    Consider key terms, user intent, and urgency level.
    </reasoning>

    <selection>
    The chosen team name (must be exactly one of: {', '.join(request.routes.keys())})
    </selection>

    Input: {request.input}""".strip()

    route_request = LLMTaskRequest(prompt=selector_prompt, model=model)
    route_response = llm_call(route_request)
    reasoning = extract_xml(route_response.response, "reasoning")
    route_key = extract_xml(route_response.response, "selection").strip().lower()

    if show_reasoning:
        print("Routing Analysis:")
        print(reasoning)
    print(f"\nSelected route: {route_key}")

    # Process input with selected specialized prompt
    selected_prompt = request.routes[route_key]
    llm_request = LLMTaskRequest(
        prompt=f"{selected_prompt}\nInput: {request.input}", model=model
    )
    output = llm_call(llm_request).response

    return RouteWorkflowResponse(
        output=output, route_key=route_key, reasoning=reasoning
    )


async def route_async(request: RouteWorkflowRequest) -> RouteWorkflowResponse:
    """Route input to specialized prompt using content classification asynchronously.

    Args:
        request: The routing request containing input, routes, and configuration

    Returns:
        RouteWorkflowResponse containing the output, selected route, and reasoning
    """
    # Use provided values or get from config
    model = request.model or config_manager.get_config_value("anthropic.route", "model")
    show_reasoning = (
        request.show_reasoning
        if request.show_reasoning is not None
        else config_manager.get_config_value("anthropic.route", "show_reasoning", True)
    )

    # First determine appropriate route using LLM with chain-of-thought
    print(f"\nAvailable routes: {list(request.routes.keys())}")
    selector_prompt = f"""
    Analyze the input and select the most appropriate support team from these options: {list(request.routes.keys())}
    First explain your reasoning, then provide your selection in this XML format:

    <reasoning>
    Brief explanation of why this ticket should be routed to a specific team.
    Consider key terms, user intent, and urgency level.
    </reasoning>

    <selection>
    The chosen team name (must be exactly one of: {', '.join(request.routes.keys())})
    </selection>

    Input: {request.input}""".strip()

    route_request = LLMTaskRequest(prompt=selector_prompt, model=model)
    route_response = await llm_call_async(route_request)
    reasoning = extract_xml(route_response.response, "reasoning")
    route_key = extract_xml(route_response.response, "selection").strip().lower()

    if show_reasoning:
        print("Routing Analysis:")
        print(reasoning)
    print(f"\nSelected route: {route_key}")

    # Process input with selected specialized prompt
    selected_prompt = request.routes[route_key]
    llm_request = LLMTaskRequest(
        prompt=f"{selected_prompt}\nInput: {request.input}", model=model
    )
    output = (await llm_call_async(llm_request)).response

    return RouteWorkflowResponse(
        output=output, route_key=route_key, reasoning=reasoning
    )
