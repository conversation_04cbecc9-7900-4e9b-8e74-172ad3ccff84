"""
Classification module for text classification operations.

This package provides implementations for classifying text using
various approaches, including RAG with Chain of Thought reasoning.

The implementation follows a modular structure with fine-grained tasks:
- Embedding tasks for text embedding
- Vector database tasks for retrieval
- LLM tasks for classification
- Functions for prompt creation and response parsing
- Workflows for composing tasks into a complete classification pipeline
"""

# Import task components
from lego_agent.agents.anthropic.skills.classification.task.embedding_task import (
    EmbeddingTaskRequest,
    EmbeddingTaskResponse,
    embed_texts,
    embed_texts_async,
)
from lego_agent.agents.anthropic.skills.classification.task.vdb_task import (
    VDBSearchRequest,
    VDBSearchResponse,
    search_vdb,
    search_vdb_async,
    VDBStoreRequest,
    VDBStoreResponse,
    store_vdb,
    store_vdb_async,
)
from lego_agent.agents.anthropic.skills.classification.task.llm_task import (
    ClassificationLLMRequest,
    ClassificationLLMResponse,
    classify_with_llm,
    classify_with_llm_async,
)

# Import function components
from lego_agent.agents.anthropic.skills.classification.function.transform import (
    extract_classification_response,
)

# Import workflow components
from lego_agent.agents.anthropic.skills.classification.workflow.rag_chain_of_thought import (
    RAGChainOfThoughtWorkflowRequest,
    RAGChainOfThoughtWorkflowResponse,
    rag_chain_of_thought_classify,
    rag_chain_of_thought_classify_async,
)

__all__ = [
    # Embedding task components
    "EmbeddingTaskRequest",
    "EmbeddingTaskResponse",
    "embed_texts",
    "embed_texts_async",
    # Vector database task components
    "VDBSearchRequest",
    "VDBSearchResponse",
    "search_vdb",
    "search_vdb_async",
    "VDBStoreRequest",
    "VDBStoreResponse",
    "store_vdb",
    "store_vdb_async",
    # LLM task components
    "ClassificationLLMRequest",
    "ClassificationLLMResponse",
    "classify_with_llm",
    "classify_with_llm_async",
    # Function components
    "extract_classification_response",
    # Workflow components
    "RAGChainOfThoughtWorkflowRequest",
    "RAGChainOfThoughtWorkflowResponse",
    "rag_chain_of_thought_classify",
    "rag_chain_of_thought_classify_async",
]
