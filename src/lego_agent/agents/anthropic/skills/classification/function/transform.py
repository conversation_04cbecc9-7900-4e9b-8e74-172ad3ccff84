"""
Transform functions for classification.

This module provides functions for transforming and parsing classification responses.
"""

import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


def extract_classification_response(response_text: str) -> Dict[str, str]:
    """Parse the classification response.

    This function tries multiple strategies to extract the category and reasoning
    from the LLM response, supporting different response formats.

    Args:
        response_text: The response text from the LLM

    Returns:
        Dictionary containing the reasoning and category
    """
    logger.debug(f"Extracting classification from response: {response_text}")

    # If response is empty or None
    if not response_text or not response_text.strip():
        logger.warning("Empty or None response text received")
        return {"reasoning": "Empty response from LLM", "category": "Unknown"}

    # Try XML format with <scratchpad> and <category> tags
    try:
        scratchpad_end = response_text.find("</scratchpad>")
        category_start = response_text.find("<category>")

        if scratchpad_end != -1 and category_start != -1:
            reasoning = response_text[
                response_text.find("<scratchpad>")
                + len("<scratchpad>") : scratchpad_end
            ].strip()
            category = response_text[
                category_start
                + len("<category>") : response_text.find("</category>", category_start)
            ].strip()

            if category and reasoning:
                return {
                    "reasoning": reasoning,
                    "category": category,
                }
    except Exception as e:
        logger.debug(f"Failed to parse XML format: {str(e)}")

    # Try to extract using the extract_xml function
    try:
        from lego_agent.agents.anthropic.patterns.function.transform import extract_xml

        response_dict = extract_xml(response_text, ["scratchpad", "category"])
        if "scratchpad" in response_dict and "category" in response_dict:
            return {
                "reasoning": response_dict["scratchpad"],
                "category": response_dict["category"],
            }
    except Exception as e:
        logger.debug(f"Failed to extract XML: {str(e)}")

    # Try to find a category in the response by looking for **bold** text
    import re

    bold_pattern = r"\*\*(.*?)\*\*"
    bold_matches = re.findall(bold_pattern, response_text)

    if bold_matches:
        # Get the last bold match (often the conclusion)
        category = bold_matches[-1].strip()
        return {"reasoning": response_text, "category": category}

    # Try to find the category by looking for the most likely category in the text
    common_categories = [
        "Coverage Explanations",
        "Billing Inquiries",
        "Account Management",
        "Billing Disputes",
        "Policy Administration",
        "Claims Assistance",
        "Claims Disputes",
        "Quotes and Proposals",
        "Policy Comparisons",
        "General Inquiries",
    ]

    for cat in common_categories:
        if cat.lower() in response_text.lower():
            return {"reasoning": response_text, "category": cat}

    # If we get here, we couldn't parse the response
    logger.warning(f"Could not parse classification response: {response_text}")
    return {
        "reasoning": response_text or "No reasoning provided",
        "category": "Unknown",
    }
