"""
Classification task module.

This package provides task implementations for classification operations.
"""

# Import embedding task components
from lego_agent.agents.anthropic.skills.classification.task.embedding_task import (
    EmbeddingTaskRequest,
    EmbeddingTaskResponse,
    embed_texts,
    embed_texts_async,
)

# Import vector database task components
from lego_agent.agents.anthropic.skills.classification.task.vdb_task import (
    VDBSearchRequest,
    VDBSearchResponse,
    search_vdb,
    search_vdb_async,
    VDBStoreRequest,
    VDBStoreResponse,
    store_vdb,
    store_vdb_async,
)

# Import LLM task components
from lego_agent.agents.anthropic.skills.classification.task.llm_task import (
    ClassificationLLMRequest,
    ClassificationLLMResponse,
    classify_with_llm,
    classify_with_llm_async,
)

__all__ = [
    # Embedding task components
    "EmbeddingTaskRequest",
    "EmbeddingTaskResponse",
    "embed_texts",
    "embed_texts_async",
    # Vector database task components
    "VDBSearchRequest",
    "VDBSearchResponse",
    "search_vdb",
    "search_vdb_async",
    "VDBStoreRequest",
    "VDBStoreResponse",
    "store_vdb",
    "store_vdb_async",
    # LLM task components
    "ClassificationLLMRequest",
    "ClassificationLLMResponse",
    "classify_with_llm",
    "classify_with_llm_async",
]
