"""
Embedding task module for text classification operations.

This module provides task implementations for embedding operations.
"""

from typing import List, Optional
import logging
from pydantic import Field

from lego_agent.core.schema.io import TaskRequest, TaskResponse
from lego_agent.core.registry_setup import get_service_manager
from lego_agent.core.service.embedding import EmbeddingServiceRequest

logger = logging.getLogger(__name__)


class EmbeddingTaskRequest(TaskRequest):
    """Request model for embedding task.

    Attributes:
        texts: List of texts to embed
        model: Embedding model to use
    """

    task_name: str = "embedding_task"

    texts: List[str] = Field(..., description="List of texts to embed")
    model: Optional[str] = Field(default=None, description="Embedding model to use")


class EmbeddingTaskResponse(TaskResponse):
    """Response model for embedding task.

    Attributes:
        embeddings: List of embeddings
        success: Whether the embedding was successful
        error: Error message if embedding failed
    """

    task_name: str = "embedding_task"

    embeddings: List[List[float]] = Field(
        default_factory=list, description="List of embeddings"
    )
    success: bool = Field(
        default=True, description="Whether the embedding was successful"
    )
    error: Optional[str] = Field(
        default=None, description="Error message if embedding failed"
    )


def embed_texts(request: EmbeddingTaskRequest) -> EmbeddingTaskResponse:
    """Embed texts using the embedding service.

    Args:
        request: The embedding request

    Returns:
        Embedding task response
    """
    logger.info(f"Embedding {len(request.texts)} texts")

    # Get service manager
    service_manager = get_service_manager()

    # Get embedding service
    embedding_service = service_manager.get_service("embedding")

    # Create embedding request
    embedding_request = EmbeddingServiceRequest(
        service_name="embedding",
        operation_name="embed",
        raw_data={
            "texts": request.texts,
            "model": request.model,
        },
    )

    try:
        # Get embeddings
        embedding_response = embedding_service.serve(embedding_request, request.model)
        embeddings = embedding_response.raw_data.get("embeddings", [])

        if not embeddings:
            logger.error("Failed to generate embeddings")
            return EmbeddingTaskResponse(
                embeddings=[],
                success=False,
                error="Failed to generate embeddings",
            )

        logger.info(f"Successfully embedded {len(embeddings)} texts")
        return EmbeddingTaskResponse(
            embeddings=embeddings,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error embedding texts: {str(e)}")
        return EmbeddingTaskResponse(
            embeddings=[],
            success=False,
            error=str(e),
        )


async def embed_texts_async(request: EmbeddingTaskRequest) -> EmbeddingTaskResponse:
    """Embed texts using the embedding service asynchronously.

    Args:
        request: The embedding request

    Returns:
        Embedding task response
    """
    logger.info(f"Embedding {len(request.texts)} texts asynchronously")

    # Get service manager
    service_manager = get_service_manager()

    # Get embedding service
    embedding_service = service_manager.get_service("embedding")

    # Create embedding request
    embedding_request = EmbeddingServiceRequest(
        service_name="embedding",
        operation_name="embed",
        raw_data={
            "texts": request.texts,
            "model": request.model,
        },
    )

    try:
        # Get embeddings
        embedding_response = await embedding_service.serve_async(
            embedding_request, request.model
        )
        embeddings = embedding_response.raw_data.get("embeddings", [])

        if not embeddings:
            logger.error("Failed to generate embeddings")
            return EmbeddingTaskResponse(
                embeddings=[],
                success=False,
                error="Failed to generate embeddings",
            )

        logger.info(f"Successfully embedded {len(embeddings)} texts asynchronously")
        return EmbeddingTaskResponse(
            embeddings=embeddings,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error embedding texts asynchronously: {str(e)}")
        return EmbeddingTaskResponse(
            embeddings=[],
            success=False,
            error=str(e),
        )
