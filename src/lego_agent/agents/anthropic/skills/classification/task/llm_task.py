"""
LLM task module for text classification operations.

This module provides task implementations for LLM operations including prompt creation
and classification using LLMs.
"""

from typing import List, Dict, Any, Optional
import logging
import textwrap
from pydantic import Field

from lego_agent.core.schema.io import TaskRequest, TaskResponse
from lego_agent.core.registry_setup import get_service_manager
from lego_agent.core.service.llm import LLMServiceRequest

logger = logging.getLogger(__name__)


class ClassificationLLMRequest(TaskRequest):
    """Request model for classification LLM task.

    Attributes:
        text: The text to classify
        categories: List of possible categories
        examples: List of retrieved examples
        model: Language model to use
        max_tokens: Maximum tokens for generation
        temperature: Temperature for generation
    """

    task_name: str = "classification_llm_task"

    text: str = Field(..., description="The text to classify")
    categories: List[str] = Field(..., description="List of possible categories")
    examples: List[Dict[str, Any]] = Field(
        default_factory=list, description="List of retrieved examples"
    )
    model: Optional[str] = Field(default=None, description="Language model to use")
    max_tokens: int = Field(default=4096, description="Maximum tokens for generation")
    temperature: float = Field(default=0.0, description="Temperature for generation")


class ClassificationLLMResponse(TaskResponse):
    """Response model for classification LLM task.

    Attributes:
        response_text: The response text from the LLM
        success: Whether the LLM call was successful
        error: Error message if LLM call failed
    """

    task_name: str = "classification_llm_task"

    response_text: str = Field(default="", description="The response text from the LLM")
    success: bool = Field(
        default=True, description="Whether the LLM call was successful"
    )
    error: Optional[str] = Field(
        default=None, description="Error message if LLM call failed"
    )


def format_examples(examples: List[Dict[str, Any]]) -> str:
    """Format examples for the prompt.

    Args:
        examples: List of retrieved examples

    Returns:
        Formatted examples string
    """
    examples_str = ""
    for example in examples:
        metadata = example.get("metadata", {})
        text = metadata.get("text", "")
        label = metadata.get("label", "")

        examples_str += textwrap.dedent(
            f"""
        <example>
            <query>
                "{text}"
            </query>
            <label>
                {label}
            </label>
        </example>
        """
        )
    return examples_str


def create_classification_prompt(
    text: str, categories: List[str], examples: List[Dict[str, Any]]
) -> str:
    """Create the classification prompt.

    Args:
        text: The text to classify
        categories: List of possible categories
        examples: List of retrieved examples

    Returns:
        Formatted prompt
    """
    categories_str = "\n        ".join(categories)
    examples_str = format_examples(examples)

    prompt = textwrap.dedent(
        f"""
    You will classify a customer support ticket into one of the following categories:
    <categories>
        {categories_str}
    </categories>

    Here is the customer support ticket:
    <ticket>
        {text}
    </ticket>

    Use the following examples to help you classify the query:
    <examples>
        {examples_str}
    </examples>

    First you will think step-by-step about the problem in scratchpad tags.
    You should consider all the information provided and create a concrete argument for your classification.

    Respond using this format:
    <response>
        <scratchpad>Your thoughts and analysis go here</scratchpad>
        <category>The category label you chose goes here</category>
    </response>
    """
    )

    return prompt


def classify_with_llm(request: ClassificationLLMRequest) -> ClassificationLLMResponse:
    """Classify text using an LLM.

    This function creates a classification prompt from the text, categories, and examples,
    then sends it to the LLM for classification.

    Args:
        request: The classification LLM request containing text, categories, and examples

    Returns:
        Classification LLM response
    """
    logger.info("Classifying text with LLM")

    # Create the classification prompt
    prompt = create_classification_prompt(
        request.text, request.categories, request.examples
    )

    # Get service manager
    service_manager = get_service_manager()

    # Get LLM service
    llm_service = service_manager.get_service("llm")

    # Create LLM request
    llm_request = LLMServiceRequest(
        service_name="llm",
        operation_name="chat",
        raw_data={
            "messages": [
                {"role": "user", "content": prompt},
                {"role": "assistant", "content": "<response><scratchpad>"},
            ],
            "stop_sequences": ["</category>"],
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "model": request.model,
        },
    )

    try:
        # Get LLM response
        llm_response = llm_service.serve(llm_request, request.model)

        # Extract the response content
        response_content = None
        if llm_response.raw_data.get("response", None) is not None:
            response_content = (
                llm_response.raw_data.get("response").choices[0].message.content
            )
        if not response_content:
            logger.error("Empty response from LLM")
            return ClassificationLLMResponse(
                response_text="",
                success=False,
                error="Empty response from LLM",
            )

        logger.info("Successfully classified text with LLM")
        return ClassificationLLMResponse(
            response_text=response_content,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error classifying text with LLM: {str(e)}")
        return ClassificationLLMResponse(
            response_text="",
            success=False,
            error=str(e),
        )


async def classify_with_llm_async(
    request: ClassificationLLMRequest,
) -> ClassificationLLMResponse:
    """Classify text using an LLM asynchronously.

    This function creates a classification prompt from the text, categories, and examples,
    then sends it to the LLM for classification asynchronously.

    Args:
        request: The classification LLM request containing text, categories, and examples

    Returns:
        Classification LLM response
    """
    logger.info("Classifying text with LLM asynchronously")

    # Create the classification prompt
    prompt = create_classification_prompt(
        request.text, request.categories, request.examples
    )

    # Get service manager
    service_manager = get_service_manager()

    # Get LLM service
    llm_service = service_manager.get_service("llm")

    # Create LLM request
    llm_request = LLMServiceRequest(
        service_name="llm",
        operation_name="chat_async",
        raw_data={
            "messages": [
                {"role": "user", "content": prompt},
                {"role": "assistant", "content": "<response><scratchpad>"},
            ],
            "stop_sequences": ["</category>"],
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "model": request.model,
        },
    )

    try:
        # Get LLM response
        llm_response = await llm_service.serve_async(llm_request, request.model)

        # Extract the response content
        response_content = None
        if llm_response.raw_data.get("response", None) is not None:
            response_content = (
                llm_response.raw_data.get("response").choices[0].message.content
            )
        if not response_content:
            logger.error("Empty response from LLM")
            return ClassificationLLMResponse(
                response_text="",
                success=False,
                error="Empty response from LLM",
            )

        logger.info("Successfully classified text with LLM asynchronously")
        return ClassificationLLMResponse(
            response_text=response_content,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error classifying text with LLM asynchronously: {str(e)}")
        return ClassificationLLMResponse(
            response_text="",
            success=False,
            error=str(e),
        )
