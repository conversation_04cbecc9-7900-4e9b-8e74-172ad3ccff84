"""
Vector database task module for text classification operations.

This module provides task implementations for vector database operations.
"""

from typing import List, Dict, Any, Optional
import logging
from pydantic import Field

from lego_agent.core.schema.io import TaskRequest, TaskResponse
from lego_agent.core.registry_setup import get_service_manager
from lego_agent.core.service.vdb import VectorDBServiceRequest

logger = logging.getLogger(__name__)


class VDBSearchRequest(TaskRequest):
    """Request model for vector database search task.

    Attributes:
        query_texts: List of query texts
        query_embeddings: List of query embeddings
        collection_name: Name of the vector database collection
        k: Number of results to return
    """

    task_name: str = "vdb_search_task"

    query_texts: List[str] = Field(
        default_factory=list, description="List of query texts"
    )
    query_embeddings: List[List[float]] = Field(
        ..., description="List of query embeddings"
    )
    collection_name: str = Field(
        default="simple", description="Name of the vector database collection"
    )
    k: int = Field(default=5, description="Number of results to return")


class VDBSearchResponse(TaskResponse):
    """Response model for vector database search task.

    Attributes:
        results: List of search results
        success: Whether the search was successful
        error: Error message if search failed
    """

    task_name: str = "vdb_search_task"

    results: List[List[Dict[str, Any]]] = Field(
        default_factory=list, description="List of search results"
    )
    success: bool = Field(default=True, description="Whether the search was successful")
    error: Optional[str] = Field(
        default=None, description="Error message if search failed"
    )


class VDBStoreRequest(TaskRequest):
    """Request model for vector database store task.

    Attributes:
        texts: List of texts to store
        embeddings: List of embeddings to store
        metadatas: List of metadata for each text
        collection_name: Name of the vector database collection
    """

    task_name: str = "vdb_store_task"

    texts: List[str] = Field(..., description="List of texts to store")
    embeddings: List[List[float]] = Field(
        ..., description="List of embeddings to store"
    )
    metadatas: List[Dict[str, Any]] = Field(
        ..., description="List of metadata for each text"
    )
    collection_name: str = Field(
        default="simple", description="Name of the vector database collection"
    )


class VDBStoreResponse(TaskResponse):
    """Response model for vector database store task.

    Attributes:
        success: Whether the store operation was successful
        error: Error message if store operation failed
    """

    task_name: str = "vdb_store_task"

    success: bool = Field(
        default=True, description="Whether the store operation was successful"
    )
    error: Optional[str] = Field(
        default=None, description="Error message if store operation failed"
    )


def search_vdb(request: VDBSearchRequest) -> VDBSearchResponse:
    """Search the vector database for similar examples.

    Args:
        request: The vector database search request

    Returns:
        Vector database search response
    """
    logger.info(
        f"Searching vector database for {len(request.query_embeddings)} queries"
    )

    # Get service manager
    service_manager = get_service_manager()

    # Get vector database service
    vdb_service = service_manager.get_service("vdb")

    # Create vector database search request
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="search",
        raw_data={
            "query_texts": request.query_texts,
            "query_embeddings": request.query_embeddings,
            "k": request.k,
            "collection_name": request.collection_name,
        },
    )

    try:
        # Search vector database
        vdb_response = vdb_service.serve(vdb_request)
        search_results = vdb_response.raw_data.get("result", [[]])

        if not search_results or not search_results[0]:
            logger.warning("No search results found")
            return VDBSearchResponse(
                results=[[]],
                success=True,
            )

        logger.info(f"Found {len(search_results[0])} search results")
        return VDBSearchResponse(
            results=search_results,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error searching vector database: {str(e)}")
        return VDBSearchResponse(
            results=[[]],
            success=False,
            error=str(e),
        )


async def search_vdb_async(request: VDBSearchRequest) -> VDBSearchResponse:
    """Search the vector database for similar examples asynchronously.

    Args:
        request: The vector database search request

    Returns:
        Vector database search response
    """
    logger.info(
        f"Searching vector database for {len(request.query_embeddings)} queries asynchronously"
    )

    # Get service manager
    service_manager = get_service_manager()

    # Get vector database service
    vdb_service = service_manager.get_service("vdb")

    # Create vector database search request
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="search_async",
        raw_data={
            "query_texts": request.query_texts,
            "query_embeddings": request.query_embeddings,
            "k": request.k,
            "collection_name": request.collection_name,
        },
    )

    try:
        # Search vector database
        vdb_response = await vdb_service.serve_async(vdb_request)
        search_results = vdb_response.raw_data.get("result", [[]])

        if not search_results or not search_results[0]:
            logger.warning("No search results found")
            return VDBSearchResponse(
                results=[[]],
                success=True,
            )

        logger.info(f"Found {len(search_results[0])} search results asynchronously")
        return VDBSearchResponse(
            results=search_results,
            success=True,
        )
    except Exception as e:
        logger.error(f"Error searching vector database asynchronously: {str(e)}")
        return VDBSearchResponse(
            results=[[]],
            success=False,
            error=str(e),
        )


def store_vdb(request: VDBStoreRequest) -> VDBStoreResponse:
    """Store texts and embeddings in the vector database.

    Args:
        request: The vector database store request

    Returns:
        Vector database store response
    """
    logger.info(f"Storing {len(request.texts)} texts in vector database")

    # Get service manager
    service_manager = get_service_manager()

    # Get vector database service
    vdb_service = service_manager.get_service("vdb")

    # Create vector database store request
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="store",
        raw_data={
            "texts": request.texts,
            "embeddings": request.embeddings,
            "metadatas": request.metadatas,
            "collection_name": request.collection_name,
        },
    )

    try:
        # Store in vector database
        vdb_response = vdb_service.serve(vdb_request)

        logger.info(
            f"Successfully stored {len(request.texts)} texts in vector database"
        )
        return VDBStoreResponse(
            success=True,
        )
    except Exception as e:
        logger.error(f"Error storing in vector database: {str(e)}")
        return VDBStoreResponse(
            success=False,
            error=str(e),
        )


async def store_vdb_async(request: VDBStoreRequest) -> VDBStoreResponse:
    """Store texts and embeddings in the vector database asynchronously.

    Args:
        request: The vector database store request

    Returns:
        Vector database store response
    """
    logger.info(f"Storing {len(request.texts)} texts in vector database asynchronously")

    # Get service manager
    service_manager = get_service_manager()

    # Get vector database service
    vdb_service = service_manager.get_service("vdb")

    # Create vector database store request
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="store_async",
        raw_data={
            "texts": request.texts,
            "embeddings": request.embeddings,
            "metadatas": request.metadatas,
            "collection_name": request.collection_name,
        },
    )

    try:
        # Store in vector database
        vdb_response = await vdb_service.serve_async(vdb_request)

        logger.info(
            f"Successfully stored {len(request.texts)} texts in vector database asynchronously"
        )
        return VDBStoreResponse(
            success=True,
        )
    except Exception as e:
        logger.error(f"Error storing in vector database asynchronously: {str(e)}")
        return VDBStoreResponse(
            success=False,
            error=str(e),
        )
