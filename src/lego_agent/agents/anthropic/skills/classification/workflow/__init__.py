"""
Classification workflow module.

This package provides workflow implementations for classification operations.
"""

from lego_agent.agents.anthropic.skills.classification.workflow.rag_chain_of_thought import (
    RAGChainOfThoughtWorkflowRequest,
    RAGChainOfThoughtWorkflowResponse,
    rag_chain_of_thought_classify,
    rag_chain_of_thought_classify_async,
)

__all__ = [
    "RAGChainOfThoughtWorkflowRequest",
    "RAGChainOfThoughtWorkflowResponse",
    "rag_chain_of_thought_classify",
    "rag_chain_of_thought_classify_async",
]
