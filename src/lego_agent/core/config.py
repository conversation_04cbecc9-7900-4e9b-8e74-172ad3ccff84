"""
Configuration management system for lego-agent.
Provides a centralized way to manage configuration settings.
"""

import os
import yaml
from typing import Dict, Any, Optional, Union, Type, TypeVar, Generic, cast
from pathlib import Path
from pydantic import BaseModel, create_model, Field

T = TypeVar("T", bound=BaseModel)


class ConfigurationError(Exception):
    """Exception raised for configuration errors."""

    pass


class ConfigurationManager:
    """Manages configuration settings for the application."""

    def __init__(self, config_dir: Optional[str] = None):
        """Initialize the configuration manager.

        Args:
            config_dir: Directory containing configuration files. Defaults to 'config'.
        """
        self.config_dir = config_dir or os.environ.get(
            "LEGO_AGENT_CONFIG_DIR", "config"
        )
        self.configs: Dict[str, Dict[str, Any]] = {}
        self._load_default_configs()

    def _load_default_configs(self) -> None:
        """Load default configuration files from the config directory."""
        config_path = Path(self.config_dir)
        if not config_path.exists():
            return

        for file_path in config_path.glob("*.yaml"):
            self.load_config(file_path.stem)

    def load_config(self, name: str) -> Dict[str, Any]:
        """Load a configuration file by name.

        Args:
            name: Name of the configuration file (without extension)

        Returns:
            The loaded configuration as a dictionary
        """
        if name in self.configs:
            return self.configs[name]
        file_path = Path(self.config_dir) / f"{name}.yaml"
        if not file_path.exists():
            raise ConfigurationError(f"Configuration file not found: {file_path}")

        try:
            with open(file_path, "r") as f:
                config = yaml.safe_load(f)
                self.configs[name] = config or {}
                return self.configs[name]
        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}")

    def get_config(self, name: str) -> Dict[str, Any]:
        """Get a configuration by name, loading it if necessary.

        Args:
            name: Name of the configuration

        Returns:
            The configuration dictionary
        """
        if name not in self.configs:
            return self.load_config(name)
        return self.configs[name]

    def get_config_value(self, name: str, key: str, default: Any = None) -> Any:
        """Get a specific configuration value.

        Args:
            name: Name of the configuration
            key: Configuration key (supports dot notation for nested configs)
            default: Default value if key is not found

        Returns:
            The configuration value or default
        """
        config = self.get_config(name)
        keys = key.split(".")

        current = config
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default

        return current

    def get_typed_config(self, name: str, model_class: Type[T]) -> T:
        """Get a typed configuration using a Pydantic model.

        Args:
            name: Name of the configuration
            model_class: Pydantic model class to validate against

        Returns:
            An instance of the model with configuration values
        """
        config = self.get_config(name)
        return model_class.model_validate(config)


def create_config_manager(config_dir: Optional[str] = None) -> ConfigurationManager:
    """Create a new configuration manager instance.

    This factory function allows creating custom configuration managers
    for testing or specialized use cases.

    Args:
        config_dir: Optional custom configuration directory

    Returns:
        A new ConfigurationManager instance
    """
    return ConfigurationManager(config_dir)


# Global configuration manager instance
config_manager = create_config_manager()
