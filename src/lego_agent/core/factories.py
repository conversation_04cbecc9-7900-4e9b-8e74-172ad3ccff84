"""
Factory functions for creating service instances.

This module centralizes all service creation logic, providing
factory functions that create properly configured service instances
based on application configuration.
"""

from .service.llm import (
    LLMService,
    LLMServiceInstanceConfig,
    LLMServiceRemoteInstance,
    LLMServiceAnthropicInstance,
)
from .service.embedding import (
    EmbeddingService,
    EmbeddingServiceInstanceConfig,
    EmbeddingServiceRemoteInstance,
    EmbeddingServiceLocalInstance,
)
from .service.vdb import (
    VectorDBService,
    VectorDBServiceInstanceConfig,
    MilvusServiceInstance,
    MilvusLiteServiceInstance,
    SimpleVectorDBServiceInstance,
)
from typing import Dict, Any, List, Optional
from .services_manager import BaseServiceManager
from .config import config_manager
import logging

logger = logging.getLogger(__name__)


def create_llm_service() -> LLMService:
    """Create a new LLM service with configuration from config files.

    This factory function creates an LLM service with instances configured
    according to the core.service.llm configuration file, handling different
    instance types based on name prefixes.

    Returns:
        A fully configured LLM service with all enabled instances
    """
    service = LLMService()

    # Get LLM configuration
    llm_config = config_manager.get_config("core.service.llm")

    # Create instances from configuration
    instances: List[Dict[str, Any]] = llm_config.get("instances", [])
    for instance_config in instances:
        # Skip disabled instances
        if not instance_config.get("enabled", True):
            continue

        name: str = instance_config.get("name")
        if not name:
            continue

        group_name: str = instance_config.get("group_name", "default")

        config = LLMServiceInstanceConfig(
            location=instance_config.get("location", "local:auto"),
            api_key=instance_config.get("api_key", None),
            model=instance_config.get("model", llm_config.get("default_model")),
        )

        # Determine instance type based on name prefix
        if name.startswith("anthropic:"):
            instance = LLMServiceAnthropicInstance(name, group_name, config)
        elif name.startswith("vllm:"):
            instance = LLMServiceRemoteInstance(name, group_name, config)
        else:
            instance = LLMServiceRemoteInstance(name, group_name, config)

        service.add_instance(instance_name=instance.instance_name, instance=instance)

    return service


def create_vdb_service() -> VectorDBService:
    """Create a new VectorDB service with configuration from config files.

    This factory function creates a VectorDB service with instances configured
    according to the core.service.vdb configuration file, handling different
    instance types based on db_type.

    Returns:
        A fully configured VectorDB service with all enabled instances
    """
    service = VectorDBService()

    try:
        # Get VDB configuration
        vdb_config = config_manager.get_config("core.service.vdb")

        # Create instances from configuration
        instances: List[Dict[str, Any]] = vdb_config.get("instances", [])
        for instance_config in instances:
            # Skip disabled instances
            if not instance_config.get("enabled", True):
                continue

            name: str = instance_config.get("name")
            if not name:
                continue

            group_name: str = instance_config.get("group_name", "default")

            # Create instance config with default values
            config = VectorDBServiceInstanceConfig(
                db_type=instance_config.get("db_type"),
                location=instance_config.get("location", "local"),
                collection_name=instance_config.get(
                    "collection_name",
                    vdb_config.get("default_collection", "default_collection"),
                ),
                dim=int(
                    instance_config.get("dim", vdb_config.get("default_dim", 1024))
                ),
                metric_type=instance_config.get(
                    "metric_type", vdb_config.get("metric_type", "L2")
                ),
                index_params=instance_config.get("index_params", {}),
                search_params=instance_config.get("search_params", {}),
            )

            # Determine instance type based on db_type
            db_type = instance_config.get("db_type")
            if db_type == "milvus":
                instance = MilvusServiceInstance(name, group_name, config)
            elif db_type == "milvus_lite":
                instance = MilvusLiteServiceInstance(name, group_name, config)
            elif db_type == "simple":
                instance = SimpleVectorDBServiceInstance(name, group_name, config)
            else:
                logger.warning(f"Unsupported VectorDB type: {db_type}")
                continue

            service.add_instance(
                instance_name=instance.instance_name, instance=instance
            )
            logger.info(f"Initialized VectorDB instance: {name} ({db_type})")

    except Exception as e:
        logger.error(f"Failed to initialize VectorDB service: {str(e)}", exc_info=True)

    return service


def create_embedding_service() -> EmbeddingService:
    """Create a new Embedding service with configuration from config files.

    This factory function creates an Embedding service with instances configured
    according to the core.service.embedding configuration file, handling different
    instance types based on name prefixes.

    Returns:
        A fully configured Embedding service with all enabled instances
    """
    service = EmbeddingService()

    # Get Embedding configuration
    embedding_config = config_manager.get_config("core.service.embedding")

    # Create instances from configuration
    instances: List[Dict[str, Any]] = embedding_config.get("instances", [])
    for instance_config in instances:
        # Skip disabled instances
        if not instance_config.get("enabled", True):
            continue

        name: str = instance_config.get("name")
        if not name:
            continue

        group_name: str = instance_config.get("group_name", "default")

        config = EmbeddingServiceInstanceConfig(
            location=instance_config.get("location", "local:auto"),
            model=instance_config.get("model", embedding_config.get("default_model")),
        )

        # Determine instance type based on name prefix
        if name.startswith("vllm:"):
            instance = EmbeddingServiceRemoteInstance(name, group_name, config)
        elif name.startswith("local"):
            instance = EmbeddingServiceLocalInstance(name, group_name, config)
        else:
            instance = EmbeddingServiceRemoteInstance(name, group_name, config)

        service.add_instance(instance_name=instance.instance_name, instance=instance)

    return service


def create_service_manager() -> BaseServiceManager:
    """Create a new service manager with default services."""
    from .di import dependency_registry

    manager = BaseServiceManager()

    # Add LLM service
    try:
        llm_service = dependency_registry.resolve(LLMService)
        manager.add_service(llm_service.service_name, llm_service)
    except Exception as e:
        logger.error(f"Failed to initialize LLM service: {str(e)}", exc_info=True)

    # Add VectorDB service
    try:
        vdb_service = dependency_registry.resolve(VectorDBService)
        manager.add_service(vdb_service.service_name, vdb_service)
    except Exception as e:
        logger.error(f"Failed to initialize VectorDB service: {str(e)}", exc_info=True)

    # Add Embedding service
    try:
        embedding_service = dependency_registry.resolve(EmbeddingService)
        manager.add_service(embedding_service.service_name, embedding_service)
    except Exception as e:
        logger.error(f"Failed to initialize Embedding service: {str(e)}", exc_info=True)

    return manager
