from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable, Literal
from pydantic import BaseModel, Field
import random
import uuid
from datetime import datetime, timezone


class BaseRequest(BaseModel):
    """Base class for all requests.

    Attributes:
        request_id (str): Unique identifier for the request (auto-generated if not provided)
        timestamp (datetime): When the request was created
        metadata (Dict[str, Any]): Additional metadata for the request
        raw_data (Dict): Operation-specific request data
    """

    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    raw_data: Dict = Field(default_factory=dict)


class BaseResponse(BaseModel):
    """Base class for all responses.

    Attributes:
        request_id (str): ID of the original request
        timestamp (datetime): When the response was created
        metadata (Dict[str, Any]): Additional metadata for the response
        raw_data (Dict): Operation-specific response data
    """

    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    raw_data: Dict = Field(default_factory=dict)


class ServiceRequest(BaseRequest):
    """Request to a service.

    Attributes:
        service_name (str): Name of the target service
        operation_name (str): Name of the operation to perform
        raw_data (Dict): Operation-specific request data
    """

    service_name: str
    operation_name: str


class ServiceResponse(BaseResponse):
    """Response from a service.

    Attributes:
        service_name (str): Name of the responding service
        operation_name (str): Name of the performed operation
        instance_name (str): Name of the specific service instance
        raw_data (Dict): Operation-specific response data
    """

    service_name: str
    operation_name: str
    instance_name: str


class TaskRequest(BaseRequest):
    """Request for a task.

    Attributes:
        task_name (str): Name of the task
        raw_data (Dict): Task-specific request data
    """

    task_name: str = ""


class TaskResponse(BaseResponse):
    """Response from a task.

    Attributes:
        task_name (str): Name of the task
        raw_data (Dict): Task-specific response data
    """

    task_name: str = ""
