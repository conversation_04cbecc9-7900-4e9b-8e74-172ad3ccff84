"""
Base service module defining service interfaces.

This module provides the base classes and interfaces for all services
in the application, establishing a consistent service architecture.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable, Literal, Optional, FrozenSet
from pydantic import BaseModel, Field, field_validator
import random
import uuid
import re
from datetime import datetime


# Supported scheduling policy types
SchedulingPolicyType = Literal[
    "round_robin", "random", "least_busy", "first_available"
]


def validate_tag_format(tag: str) -> bool:
    """Validate that a tag follows the allowed format.

    Tags must be non-empty strings containing only alphanumeric characters,
    underscores, hyphens, forward slashes, and dots (to support model names).

    Args:
        tag: The tag string to validate

    Returns:
        True if the tag is valid, False otherwise
    """
    if not tag or not isinstance(tag, str):
        return False
    return bool(re.match(r'^[a-zA-Z0-9_/.-]+$', tag))


class BaseServiceInstanceConfig(BaseModel):
    """Configuration for a service instance.

    Attributes:
        location (str): Location of the service instance (local:auto or ip:port)
        api_key (str): API key for the service instance (optional)
        tags (FrozenSet[str]): Set of tags for categorizing this instance
    """

    location: str = "local:auto"  # or "ip:port"
    api_key: Optional[str] = None
    tags: FrozenSet[str] = Field(default_factory=frozenset)

    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Validate that all tags follow the allowed format."""
        if v is None:
            return frozenset()

        # Convert to frozenset if it's not already
        if not isinstance(v, frozenset):
            v = frozenset(v) if v else frozenset()

        # Validate all tags
        invalid_tags = [tag for tag in v if not validate_tag_format(tag)]
        if invalid_tags:
            raise ValueError(
                f"Invalid tag format(s): {invalid_tags}. Tags must be "
                "non-empty strings containing only alphanumeric characters, "
                "underscores, hyphens, forward slashes, and dots."
            )
        return v


class BaseServiceRequest(BaseModel):
    """Request to a service.

    Attributes:
        service_name (str): Name of the target service
        operation_name (str): Name of the operation to perform
        raw_data (Dict): Operation-specific request data
        request_id (str): Unique identifier for the request (auto-generated if not provided)
        timestamp (datetime): When the request was created
        metadata (Dict): Additional metadata for the request
    """

    service_name: str
    operation_name: str
    raw_data: Dict = Field(default_factory=dict)
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class BaseServiceResponse(BaseModel):
    """Response from a service.

    Attributes:
        service_name (str): Name of the responding service
        instance_name (str): Name of the specific service instance
        operation_name (str): Name of the performed operation
        raw_data (Dict): Operation-specific response data
        request_id (str): ID of the original request
        timestamp (datetime): When the response was created
        metadata (Dict): Additional metadata for the response
    """

    service_name: str
    instance_name: str
    operation_name: str
    raw_data: Dict = Field(default_factory=dict)
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class BaseServiceInstance(ABC):
    def __init__(
        self,
        service_name: str,
        instance_name: str,
        group_name: str,
        config: BaseServiceInstanceConfig,
    ):
        """Initialize a service instance.

        Args:
            service_name: Name of the service this instance belongs to
            instance_name: Unique name for this instance
            group_name: Name of the group this instance belongs to
            config: Configuration for this instance (includes tags)

        Raises:
            ValueError: If any tag has an invalid format (validated by config)
        """
        self.service_name: str = service_name
        self.instance_name: str = instance_name
        self.group_name: str = group_name
        self.config: BaseServiceInstanceConfig = config
        self.active_requests: int = 0
        self.total_requests: int = 0

        # Get tags from config (validation already done by Pydantic)
        self.tags: FrozenSet[str] = config.tags

    @abstractmethod
    def launch(self):
        pass

    @abstractmethod
    def shutdown(self):
        pass

    @abstractmethod
    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Implementation of serve logic in subclasses."""
        pass

    @abstractmethod
    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        """Implementation of async serve logic in subclasses."""
        pass

    def serve(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Serve a request with request tracking.

        This method handles the tracking of active and total requests,
        delegating the actual implementation to _serve_impl.

        Args:
            request: The service request to serve

        Returns:
            The service response
        """
        self.active_requests += 1
        self.total_requests += 1

        try:
            response = self._serve_impl(request)
            response.instance_name = self.instance_name
            return response
        finally:
            self.active_requests -= 1

    async def serve_async(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Serve a request asynchronously with request tracking.

        This method handles the tracking of active and total requests,
        delegating the actual implementation to _serve_async_impl.

        Args:
            request: The service request to serve

        Returns:
            The service response
        """
        self.active_requests += 1
        self.total_requests += 1

        try:
            response = await self._serve_async_impl(request)
            response.instance_name = self.instance_name
            return response
        finally:
            self.active_requests -= 1

    def __call__(self, request: BaseServiceRequest) -> BaseServiceResponse:
        return self.serve(request)

    async def __call_async__(self, request: BaseServiceRequest) -> BaseServiceResponse:
        return await self.serve_async(request)


class BaseService:
    def __init__(
        self, service_name: str, scheduling_policy: SchedulingPolicyType = "round_robin"
    ):
        self.service_name: str = service_name
        self.instances: Dict[str, BaseServiceInstance] = {}
        self.instance_names: List[str] = []
        self.instance_groups: Dict[str, str] = {}
        self.scheduling_policy: SchedulingPolicyType = scheduling_policy
        # Track instances by tags for efficient tag-based scheduling
        self.tag_to_instances: Dict[str, List[str]] = {}
        # Track round-robin indices for different instance groups
        self.round_robin_indices: Dict[str, int] = {"default": 0}
        # Map of scheduling policy names to their implementation functions
        self.scheduling_policies: Dict[str, Callable] = {
            "round_robin": self._round_robin_policy,
            "random": self._random_policy,
            "least_busy": self._least_busy_policy,
            "first_available": self._first_available_policy,
        }

    def set_scheduling_policy(self, policy: SchedulingPolicyType) -> None:
        """Set the scheduling policy for this service.

        Args:
            policy: The scheduling policy to use
        """
        if policy not in self.scheduling_policies:
            raise ValueError(f"Unsupported scheduling policy: {policy}")
        self.scheduling_policy = policy

    def add_instance(self, instance_name: str, instance: BaseServiceInstance) -> None:
        if instance_name in self.instances:
            raise Exception("Instance already exists")
        self.instances[instance_name] = instance
        self.instance_names.append(instance_name)
        self.instance_groups[instance_name] = instance.group_name

        # Add to tag-based lookup
        for tag in instance.tags:
            if tag not in self.tag_to_instances:
                self.tag_to_instances[tag] = []
            self.tag_to_instances[tag].append(instance_name)

    def remove_instance(self, instance_name: str) -> None:
        instance = self.instances.get(instance_name)
        if instance:
            # Remove from tag-based lookup
            for tag in instance.tags:
                if tag in self.tag_to_instances:
                    if instance_name in self.tag_to_instances[tag]:
                        self.tag_to_instances[tag].remove(instance_name)
                    # Clean up empty tag lists
                    if not self.tag_to_instances[tag]:
                        del self.tag_to_instances[tag]

        # Remove from main collections
        del self.instances[instance_name]
        self.instance_names.remove(instance_name)
        del self.instance_groups[instance_name]

    def get_instance(self, instance_name: str) -> BaseServiceInstance:
        instance = self.instances.get(instance_name)
        if instance is None:
            raise Exception(f"Instance {instance_name} not found")
        return instance

    def get_instance_list(self) -> List[BaseServiceInstance]:
        return list(self.instances.values())

    def get_instance_count(self) -> int:
        return len(self.instances)

    def _filter_instances_by_tags(
        self, instance_names: List[str], required_tags: FrozenSet[str]
    ) -> List[str]:
        """Filter instances that have all required tags.

        Args:
            instance_names: List of instance names to filter
            required_tags: Set of tags that instances must have (subset match)

        Returns:
            List of instance names that have all required tags
        """
        if not required_tags:
            return instance_names

        filtered_instances = []
        for instance_name in instance_names:
            instance = self.instances[instance_name]
            # Check if instance has all required tags (subset match)
            if required_tags.issubset(instance.tags):
                filtered_instances.append(instance_name)

        return filtered_instances

    # Scheduling policy implementations
    def _round_robin_policy(self, instance_names: List[str]) -> str:
        """Round-robin scheduling policy."""
        if not instance_names:
            raise Exception("No instance available")

        # Use a hash of the instance names as a key for this specific group
        group_key = str(hash(tuple(sorted(instance_names))))

        # Initialize index for this group if needed
        if group_key not in self.round_robin_indices:
            self.round_robin_indices[group_key] = 0

        # Get current index and instance
        current_index = self.round_robin_indices[group_key]
        instance_name = instance_names[current_index]

        # Update index for next time
        self.round_robin_indices[group_key] = (current_index + 1) % len(instance_names)

        return instance_name

    def _random_policy(self, instance_names: List[str]) -> str:
        """Random scheduling policy."""
        if not instance_names:
            raise Exception("No instance available")
        return random.choice(instance_names)

    def _least_busy_policy(self, instance_names: List[str]) -> str:
        """Least busy scheduling policy - select instance with fewest active requests."""
        if not instance_names:
            raise Exception("No instance available")

        return min(
            instance_names, key=lambda name: self.instances[name].active_requests
        )

    def _first_available_policy(self, instance_names: List[str]) -> str:
        """First available scheduling policy - select first instance with no active requests."""
        if not instance_names:
            raise Exception("No instance available")

        # Try to find an instance with no active requests
        for name in instance_names:
            if self.instances[name].active_requests == 0:
                return name

        # Fall back to least busy if all are busy
        return self._least_busy_policy(instance_names)

    def schedule(
        self,
        request: BaseServiceRequest,
        instance_name: str = None,
        tags: Optional[FrozenSet[str]] = None,
    ) -> BaseServiceInstance:
        """Schedule a request to an appropriate instance.

        Args:
            request: The service request to schedule
            instance_name: Optional instance name override. If provided, uses this instance if available
            tags: Optional set of tags. If provided, only instances with all these tags will be considered

        Returns:
            The selected service instance
        """
        # Determine which instances to consider
        if instance_name is not None:
            if instance_name in self.instances:
                return self.instances[instance_name]
            else:
                raise Exception(f"Instance {instance_name} not found")

        # Start with all instances
        candidate_instances = self.instance_names.copy()

        # Apply tag filtering if tags are specified
        if tags:
            candidate_instances = self._filter_instances_by_tags(candidate_instances, tags)

        if not candidate_instances:
            raise Exception("No suitable instance available")

        # Use the configured scheduling policy
        policy_func = self.scheduling_policies.get(self.scheduling_policy)
        if not policy_func:
            raise NotImplementedError(
                f"Scheduling policy {self.scheduling_policy} not implemented"
            )

        selected_name = policy_func(candidate_instances)
        return self.instances[selected_name]

    def serve(
        self,
        request: BaseServiceRequest,
        instance_name: str = None,
        tags: Optional[FrozenSet[str]] = None,
    ) -> BaseServiceResponse:
        """Serve a request with optional routing parameters.

        Args:
            request: The service request to serve
            instance_name: Optional instance name override
            tags: Optional set of tags for instance filtering

        Returns:
            The service response
        """
        instance = self.schedule(request, instance_name, tags)
        return instance.serve(request)

    async def serve_async(
        self,
        request: BaseServiceRequest,
        instance_name: str = None,
        tags: Optional[FrozenSet[str]] = None,
    ) -> BaseServiceResponse:
        """Serve a request asynchronously with optional routing parameters.

        Args:
            request: The service request to serve
            tags: Optional set of tags for instance filtering

        Returns:
            The service response
        """
        instance = self.schedule(request, instance_name, tags)
        return await instance.serve_async(request)

    def __call__(
        self,
        request: BaseServiceRequest,
        instance_name: str = None,
        tags: Optional[FrozenSet[str]] = None,
    ) -> BaseServiceResponse:
        return self.serve(request, instance_name, tags)

    async def __call_async__(
        self,
        request: BaseServiceRequest,
        instance_name: str = None,
        tags: Optional[FrozenSet[str]] = None,
    ) -> BaseServiceResponse:
        return await self.serve_async(request, instance_name, tags)
