"""
Embedding service module for text embedding operations.

This module provides service implementations for generating embeddings
from text using various embedding models, including remote API-based models
and local models running in containers.
"""

from .base import (
    BaseServiceInstanceConfig,
    BaseServiceInstance,
    BaseService,
    BaseServiceRequest,
    BaseServiceResponse,
    BaseModel,
)
import os
from abc import abstractmethod
from typing import Iterable, List, Union, cast


class EmbeddingServiceRequest(BaseServiceRequest):
    """Request model for Embedding service operations."""

    pass


class EmbeddingServiceResponse(BaseServiceResponse):
    """Response model for Embedding service operations."""

    pass


class EmbeddingServiceInstanceConfig(BaseServiceInstanceConfig):
    """Configuration for an Embedding service instance.

    Attributes:
        model: Model name or identifier
    """

    model: str


class EmbeddingServiceInstance(BaseServiceInstance):
    """Base class for Embedding service instances.

    This abstract class defines the interface that all Embedding service
    implementations must provide, including methods for generating embeddings
    in both synchronous and asynchronous forms.
    """

    @abstractmethod
    def embed(self, model: str, texts: Iterable[str], **kwargs) -> List[List[float]]:
        """Generate embeddings for a list of texts.

        Args:
            model: The model to use for embedding generation
            texts: The list of texts to embed
            **kwargs: Additional model-specific parameters

        Returns:
            List of embeddings, where each embedding is a list of floats
        """
        raise NotImplementedError

    @abstractmethod
    async def embed_async(
        self, model: str, texts: Iterable[str], **kwargs
    ) -> List[List[float]]:
        """Generate embeddings for a list of texts asynchronously.

        Args:
            model: The model to use for embedding generation
            texts: The list of texts to embed
            **kwargs: Additional model-specific parameters

        Returns:
            List of embeddings, where each embedding is a list of floats
        """
        raise NotImplementedError

    def embed_single(self, model: str, text: str, **kwargs) -> List[float]:
        """Generate embedding for a single text.

        Args:
            model: The model to use for embedding generation
            text: The text to embed
            **kwargs: Additional model-specific parameters

        Returns:
            The embedding as a list of floats
        """
        return self.embed(model, [text], **kwargs)[0]

    async def embed_single_async(self, model: str, text: str, **kwargs) -> List[float]:
        """Generate embedding for a single text asynchronously.

        Args:
            model: The model to use for embedding generation
            text: The text to embed
            **kwargs: Additional model-specific parameters

        Returns:
            The embedding as a list of floats
        """
        embeddings = await self.embed_async(model, [text], **kwargs)
        return embeddings[0]

    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Serve a request."""
        # Cast to the specific request type for type checking
        embedding_request = cast(EmbeddingServiceRequest, request)
        operation_name = embedding_request.operation_name
        raw_data = embedding_request.raw_data

        if operation_name == "embed":
            result = self.embed(**raw_data)
        elif operation_name == "embed_single":
            result = self.embed_single(**raw_data)
        else:
            raise ValueError(f"Unknown operation name: {operation_name}")

        return EmbeddingServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=operation_name,
            raw_data={"embeddings": result},
        )

    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        """Serve a request asynchronously."""
        # Cast to the specific request type for type checking
        embedding_request = cast(EmbeddingServiceRequest, request)
        operation_name = embedding_request.operation_name
        raw_data = embedding_request.raw_data

        if operation_name == "embed" or operation_name == "embed_async":
            result = await self.embed_async(**raw_data)
        elif operation_name == "embed_single" or operation_name == "embed_single_async":
            result = await self.embed_single_async(**raw_data)
        else:
            raise ValueError(f"Unknown operation name: {operation_name}")

        return EmbeddingServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=operation_name,
            raw_data={"embeddings": result},
        )


class EmbeddingServiceRemoteInstance(EmbeddingServiceInstance):
    """Embedding service implementation for remote OpenAI-compatible API."""

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: EmbeddingServiceInstanceConfig,
    ):
        super().__init__("embedding", instance_name, group_name, config)
        self.launch()

    def get_api_key(self):
        """Get the API key from environment variables."""
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key is None:
            raise ValueError("OPENAI_API_KEY is not set")
        return api_key

    def get_base_url(self):
        """Get the base URL for the API."""
        ip, port = self.config.location.split(":")
        return f"http://{ip}:{port}/v1"

    def launch(self):
        """Initialize the OpenAI client."""
        from openai import OpenAI, AsyncOpenAI

        self.client = OpenAI(
            api_key=self.get_api_key(),
            base_url=self.get_base_url(),
        )
        self.async_client = AsyncOpenAI(
            api_key=self.get_api_key(),
            base_url=self.get_base_url(),
        )

    def shutdown(self):
        """Clean up resources."""
        pass

    def embed(self, model: str, texts: Iterable[str], **kwargs) -> List[List[float]]:
        """Generate embeddings using the OpenAI API."""
        response = self.client.embeddings.create(
            model=model,
            input=list(texts),
            **kwargs,
        )
        return [item.embedding for item in response.data]

    async def embed_async(
        self, model: str, texts: Iterable[str], **kwargs
    ) -> List[List[float]]:
        """Generate embeddings asynchronously using the OpenAI API."""
        response = await self.async_client.embeddings.create(
            model=model,
            input=list(texts),
            **kwargs,
        )
        return [item.embedding for item in response.data]


class EmbeddingServiceLocalInstance(EmbeddingServiceInstance):
    """Embedding service implementation for local models."""

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: EmbeddingServiceInstanceConfig,
    ):
        super().__init__("embedding", instance_name, group_name, config)
        self.launch()

    def launch(self):
        """Initialize the local embedding model."""
        try:
            from sentence_transformers import SentenceTransformer

            model = self.config.model
            device = self.config.location

            self.model = SentenceTransformer(model, device=device)
        except ImportError:
            raise ImportError(
                "SentenceTransformer package is required for local embedding models. "
                "Install it with: pip install sentence-transformers"
            )

    def shutdown(self):
        """Clean up resources."""
        pass

    def embed(self, model: str, texts: Iterable[str], **kwargs) -> List[List[float]]:
        """Generate embeddings using the local model."""
        # model parameter is ignored as we're using the pre-loaded model
        embeddings = self.model.encode(list(texts), **kwargs)
        return embeddings.tolist()

    async def embed_async(
        self, model: str, texts: Iterable[str], **kwargs
    ) -> List[List[float]]:
        """Generate embeddings asynchronously using the local model."""
        import asyncio

        # Run the synchronous embedding in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, lambda: self.embed(model, texts, **kwargs)
        )


class EmbeddingService(BaseService):
    """Embedding service for managing embedding service instances."""

    def __init__(self, scheduling_policy: str = "round_robin"):
        super().__init__("embedding", scheduling_policy)
