"""
LLM service module for language model interactions.

This module provides service implementations for interacting with
various language models, including remote API-based models and
local models running in containers.
"""

from numpy.core.multiarray import result_type
from .base import (
    BaseServiceInstanceConfig,
    BaseServiceInstance,
    BaseService,
    BaseServiceRequest,
    BaseServiceResponse,
    BaseModel,
)
import os
from abc import abstractmethod
from typing import Iterable, List, Union, cast


class LLMServiceRequest(BaseServiceRequest):
    pass


class LLMServiceResponse(BaseServiceResponse):
    pass


class LLMServiceInstanceConfig(BaseServiceInstanceConfig):
    model: str


class LLMServiceInstance(BaseServiceInstance):
    """Base class for LLM service instances.

    This abstract class defines the interface that all LLM service
    implementations must provide, including methods for chat completions
    and text completions in both synchronous and asynchronous forms.
    """

    @abstractmethod
    def chat(self, model: str, messages: Iterable[dict], **kwargs) -> BaseModel:
        """Generate a chat completion.

        Args:
            model: The model to use for generation
            messages: The conversation history as a list of message dictionaries
            **kwargs: Additional model-specific parameters

        Returns:
            The model's response as a BaseModel object
        """
        raise NotImplementedError

    @abstractmethod
    async def chat_async(
        self, model: str, messages: Iterable[dict], **kwargs
    ) -> BaseModel:
        """Generate a chat completion asynchronously.

        Args:
            model: The model to use for generation
            messages: The conversation history as a list of message dictionaries
            **kwargs: Additional model-specific parameters

        Returns:
            The model's response as a BaseModel object
        """
        raise NotImplementedError

    @abstractmethod
    def text_completion(
        self,
        model: str,
        prompt: Union[str, List[str], Iterable[int], Iterable[Iterable[int]], None],
        **kwargs,
    ) -> BaseModel:
        raise NotImplementedError

    @abstractmethod
    async def text_completion_async(
        self,
        model: str,
        prompt: Union[str, List[str], Iterable[int], Iterable[Iterable[int]], None],
        **kwargs,
    ) -> BaseModel:
        raise NotImplementedError

    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Serve a request."""
        # Cast to the specific request type for type checking
        llm_request = cast(LLMServiceRequest, request)
        operation_name = llm_request.operation_name
        raw_data = llm_request.raw_data

        if operation_name == "chat":
            result = self.chat(**raw_data)
        elif operation_name == "text_completion":
            result = self.text_completion(**raw_data)
        else:
            raise ValueError(f"Unknown operation name: {operation_name}")

        return LLMServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=operation_name,
            raw_data={"response": result},
        )

    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        """Serve a request asynchronously."""
        # Cast to the specific request type for type checking
        llm_request = cast(LLMServiceRequest, request)
        operation_name = llm_request.operation_name
        raw_data = llm_request.raw_data

        if operation_name == "chat" or operation_name == "chat_async":
            result = await self.chat_async(**raw_data)
        elif (
            operation_name == "text_completion"
            or operation_name == "text_completion_async"
        ):
            result = await self.text_completion_async(**raw_data)
        else:
            raise ValueError(f"Unknown operation name: {operation_name}")

        return LLMServiceResponse(
            service_name=self.service_name,
            instance_name=self.instance_name,
            operation_name=operation_name,
            raw_data={"response": result},
        )


class LLMServiceRemoteInstance(LLMServiceInstance):
    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: LLMServiceInstanceConfig,
    ):
        super().__init__("llm", instance_name, group_name, config)
        self.launch()

    def get_api_key(self):
        api_key = self.config.api_key
        if api_key is not None:
            return api_key
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key is None:
            raise ValueError("OPENAI_API_KEY is not set")
        return api_key

    def get_base_url(self):
        ip, port = self.config.location.split(":")
        return f"http://{ip}:{port}/v1"

    def launch(self):
        from openai import OpenAI, AsyncOpenAI

        self.client = OpenAI(
            api_key=self.get_api_key(),
            base_url=self.get_base_url(),
        )
        self.async_client = AsyncOpenAI(
            api_key=self.get_api_key(),
            base_url=self.get_base_url(),
        )

    def chat(self, model: str, messages: Iterable[dict], **kwargs) -> BaseModel:
        return self.client.chat.completions.create(
            messages=messages,
            model=model,
            temperature=kwargs.get("temperature", 0.0),
            seed=kwargs.get("seed", None),
            max_tokens=kwargs.get("max_tokens", None),
        )

    async def chat_async(
        self, model: str, messages: Iterable[dict], **kwargs
    ) -> BaseModel:
        return await self.async_client.chat.completions.create(
            messages=messages,
            model=model,
            temperature=kwargs.get("temperature", 0.0),
            seed=kwargs.get("seed", None),
            max_tokens=kwargs.get("max_tokens", None),
        )

    def text_completion(
        self,
        model: str,
        prompt: Union[str, List[str], Iterable[int], Iterable[Iterable[int]], None],
        **kwargs,
    ) -> BaseModel:
        return self.client.completions.create(
            model=model,
            prompt=prompt,
            temperature=kwargs.get("temperature", 0.0),
            seed=kwargs.get("seed", None),
            max_tokens=kwargs.get("max_tokens", None),
        )

    async def text_completion_async(
        self,
        model: str,
        prompt: Union[str, List[str], Iterable[int], Iterable[Iterable[int]], None],
        **kwargs,
    ) -> BaseModel:
        return await self.async_client.completions.create(
            model=model,
            prompt=prompt,
            temperature=kwargs.get("temperature", 0.0),
            seed=kwargs.get("seed", None),
            max_tokens=kwargs.get("max_tokens", None),
        )

    def shutdown(self):
        pass


class LLMServiceAnthropicInstance(LLMServiceRemoteInstance):
    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: LLMServiceInstanceConfig,
    ):
        super().__init__(instance_name, group_name, config)
        self.launch()

    def get_api_key(self):
        api_key = os.getenv("ANTHROPIC_API_KEY")
        if api_key is None:
            raise ValueError("ANTHROPIC_API_KEY is not set")
        return api_key

    def launch(self):
        from anthropic import Anthropic

        self.client = Anthropic(api_key=self.get_api_key())


class LLMServiceLocalVLLMInstance(LLMServiceInstance):
    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: LLMServiceInstanceConfig,
    ):
        super().__init__("llm", instance_name, group_name, config)
        self.launch()


class LLMServiceLocalSGLangInstance(LLMServiceInstance):
    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: LLMServiceInstanceConfig,
    ):
        super().__init__("llm", instance_name, group_name, config)
        self.launch()


class LLMService(BaseService):
    def __init__(self):
        super().__init__("llm")
