"""
VectorDB service module for vector database interactions.

This module provides service implementations for interacting with
various vector databases, including remote Milvus and local Milvus Lite.
"""

import logging
from typing import (
    Dict,
    List,
    Optional,
    Any,
    Union,
    Literal,
    Tuple,
    cast,
    Iterable,
    Awaitable,
    override,
)
from pydantic import BaseModel, Field
import numpy as np
import uuid
import os
import asyncio
from abc import abstractmethod
from pymilvus import MilvusClient, utility, AsyncMilvusClient

# Configure logger
logger = logging.getLogger(__name__)

from .base import (
    BaseServiceInstanceConfig,
    BaseServiceInstance,
    BaseService,
    BaseServiceRequest,
    BaseServiceResponse,
)


class VectorDBServiceRequest(BaseServiceRequest):
    """Request model for VectorDB service operations."""

    pass


class VectorDBServiceResponse(BaseServiceResponse):
    """Response model for VectorDB service operations."""

    pass


class VectorDBServiceInstanceConfig(BaseServiceInstanceConfig):
    """Configuration for a VectorDB service instance.

    Attributes:
        model: Model name or identifier
        db_type: Type of the vector database ('milvus' or 'milvus_lite')
        collection_name: Name of the collection to work with
        dim: Dimensionality of the vectors
        index_params: Parameters for vector index
        search_params: Parameters for vector search
        metric_type: Distance metric type ('L2', 'IP', 'JACCARD', etc.)
        location: Connection string for the database (e.g., 'localhost:19530')
    """

    model: str = "default_model"
    db_type: Literal["milvus", "milvus_lite", "simple"] = "milvus"
    collection_name: str = "default_collection"
    dim: int = 1024
    index_params: Dict[str, Any] = Field(default_factory=dict)
    search_params: Dict[str, Any] = Field(default_factory=dict)
    metric_type: str = "L2"
    location: str = "localhost:19530"


class VectorDBServiceInstance(BaseServiceInstance):
    """Base class for VectorDB service instances.

    This abstract class defines the interface that all VectorDB service
    implementations must provide for vector database operations.
    """

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: VectorDBServiceInstanceConfig,
    ):
        super().__init__("vdb", instance_name, group_name, config)
        self.collection = None
        self.collection_name = getattr(config, "collection_name", "default_collection")
        self.dim = getattr(config, "dim", 1024)
        self.metric_type = getattr(config, "metric_type", "L2")

        # Initialize index parameters with defaults if not provided
        self.index_params = getattr(config, "index_params", None) or {
            "index_type": "IVF_FLAT",
            "metric_type": self.metric_type,
            "params": {"nlist": 128},
        }

        # Initialize search parameters with defaults if not provided
        self.search_params = getattr(config, "search_params", None) or {
            "metric_type": self.metric_type,
            "params": {"nprobe": 10},
        }

    @abstractmethod
    def create_collection(self, collection_name: str, dim: int, **kwargs) -> bool:
        """Create a new collection.

        Args:
            collection_name: Name of the collection to create
            dim: Dimensionality of the vectors
            **kwargs: Additional collection parameters

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError

    @abstractmethod
    def store(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        """Insert vectors into the collection.

        Args:
            texts: List of texts to insert
            embeddings: List of embeddings to insert
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional arguments

        Returns:
            List of inserted IDs
        """
        raise NotImplementedError

    @abstractmethod
    def search(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        """Search for similar vectors in the collection.

        Args:
            query_texts: List of query texts
            query_embeddings: List of query embeddings
            k: Number of results to return per query
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional search parameters

        Returns:
            List of lists of search results (one list per query)
        """
        raise NotImplementedError

    @abstractmethod
    def drop_collection(self, collection_name: str, **kwargs) -> bool:
        """Drop a collection.

        Args:
            collection_name: Name of the collection to drop
            **kwargs: Additional parameters

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError

    @abstractmethod
    async def create_collection_async(
        self, collection_name: str, dim: int, **kwargs
    ) -> bool:
        """Create a new collection asynchronously.

        Args:
            collection_name: Name of the collection to create
            dim: Dimensionality of the vectors
            **kwargs: Additional collection parameters

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError

    @abstractmethod
    async def store_async(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        """Insert vectors into the collection asynchronously.

        Args:
            texts: List of texts to insert
            embeddings: List of embeddings to insert
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional arguments

        Returns:
            List of inserted IDs
        """
        raise NotImplementedError

    @abstractmethod
    async def search_async(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        """Search for similar vectors in the collection asynchronously.

        Args:
            query_texts: List of query texts
            query_embeddings: List of query embeddings
            k: Number of results to return per query
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional search parameters

        Returns:
            List of lists of search results (one list per query)
        """
        raise NotImplementedError

    @abstractmethod
    async def drop_collection_async(self, collection_name: str, **kwargs) -> bool:
        """Drop a collection asynchronously.

        Args:
            collection_name: Name of the collection to drop
            **kwargs: Additional parameters

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError

    def search_single(
        self,
        query_text: str,
        query_embedding: list[float],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[Dict]:
        """Search for similar vectors in the collection.

        Args:
            query_text: Query text
            query_embedding: Query embedding
            k: Number of results to return
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional search parameters

        Returns:
            List of search results
        """

        return self.search(
            [query_text], [query_embedding], k, collection_name, **kwargs
        )[0]

    def similarity_search(
        self,
        query_text: str,
        query_embedding: list[float],
        k: int = 5,
        **kwargs,
    ) -> List[Dict]:
        """Deprecated interface for search_single"""

        return self.search_single(query_text, query_embedding, k, **kwargs)

    def batch_similarity_search(
        self,
        queries: list[str],
        embeddings: list[list[float]],
        k: int = 4,
        **kwargs: Any,
    ) -> List[List[Dict]]:
        """Deprecated interface for search."""

        return self.search(queries, embeddings, k, **kwargs)

    async def search_single_async(
        self,
        query_text: str,
        query_embedding: list[float],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[Dict]:
        """Search for similar vectors in the collection asynchronously.

        Args:
            query_text: Query text
            query_embedding: Query embedding
            k: Number of results to return
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional search parameters

        Returns:
            List of search results
        """

        results = await self.search_async(
            [query_text], [query_embedding], k, collection_name, **kwargs
        )
        return results[0]

    async def similarity_search_async(
        self,
        query_text: str,
        query_embedding: list[float],
        k: int = 5,
        **kwargs,
    ) -> List[Dict]:
        """Deprecated interface for search_single_async"""

        return await self.search_single_async(query_text, query_embedding, k, **kwargs)

    async def batch_similarity_search_async(
        self,
        queries: list[str],
        embeddings: list[list[float]],
        k: int = 4,
        **kwargs: Any,
    ) -> List[List[Dict]]:
        """Deprecated interface for search_async."""

        return await self.search_async(queries, embeddings, k, **kwargs)

    def _serve_impl(self, request: BaseServiceRequest) -> BaseServiceResponse:
        """Handle incoming service requests."""
        vdb_request = cast(VectorDBServiceRequest, request)
        operation_name = vdb_request.operation_name
        raw_data = vdb_request.raw_data

        try:
            if operation_name == "create_collection":
                result = self.create_collection(**raw_data)
            elif operation_name == "store":
                result = self.store(**raw_data)
            elif operation_name == "search":
                result = self.search(**raw_data)
            elif operation_name == "search_single":
                result = self.search_single(**raw_data)
            elif operation_name == "drop_collection":
                result = self.drop_collection(**raw_data)
            elif operation_name == "similarity_search":
                result = self.similarity_search(**raw_data)
            elif operation_name == "batch_similarity_search":
                result = self.batch_similarity_search(**raw_data)
            else:
                raise ValueError(f"Unknown operation: {operation_name}")

            return VectorDBServiceResponse(
                service_name=self.service_name,
                instance_name=self.instance_name,
                operation_name=operation_name,
                raw_data={"result": result},
            )
        except Exception as e:
            return VectorDBServiceResponse(
                service_name=self.service_name,
                instance_name=self.instance_name,
                operation_name=operation_name,
                raw_data={"error": str(e)},
            )

    async def _serve_async_impl(
        self, request: BaseServiceRequest
    ) -> BaseServiceResponse:
        """Handle incoming async service requests."""
        vdb_request = cast(VectorDBServiceRequest, request)
        operation_name = vdb_request.operation_name
        raw_data = vdb_request.raw_data

        try:
            if (
                operation_name == "create_collection"
                or operation_name == "create_collection_async"
            ):
                result = await self.create_collection_async(**raw_data)
            elif operation_name == "store" or operation_name == "store_async":
                result = await self.store_async(**raw_data)
            elif operation_name == "search" or operation_name == "search_async":
                result = await self.search_async(**raw_data)
            elif (
                operation_name == "search_single"
                or operation_name == "search_single_async"
            ):
                result = await self.search_single_async(**raw_data)
            elif (
                operation_name == "drop_collection"
                or operation_name == "drop_collection_async"
            ):
                result = await self.drop_collection_async(**raw_data)
            elif (
                operation_name == "similarity_search"
                or operation_name == "similarity_search_async"
            ):
                result = await self.similarity_search_async(**raw_data)
            elif (
                operation_name == "batch_similarity_search"
                or operation_name == "batch_similarity_search_async"
            ):
                result = await self.batch_similarity_search_async(**raw_data)
            else:
                # Fallback to sync implementation for unknown operations
                return self._serve_impl(request)

            return VectorDBServiceResponse(
                service_name=self.service_name,
                instance_name=self.instance_name,
                operation_name=operation_name,
                raw_data={"result": result},
            )
        except Exception as e:
            return VectorDBServiceResponse(
                service_name=self.service_name,
                instance_name=self.instance_name,
                operation_name=operation_name,
                raw_data={"error": str(e)},
            )


class MilvusServiceInstance(VectorDBServiceInstance):
    """VectorDB service implementation for remote Milvus."""

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: VectorDBServiceInstanceConfig,
    ):
        # Initialize base class first which will set up all the common attributes
        super().__init__(instance_name, group_name, config)

        # Initialize Milvus-specific attributes
        self.client = None
        self.async_client = None
        self.collection = None
        self.launch()

    def launch(self):
        """Initialize the Milvus client using MilvusClient."""
        from pymilvus import MilvusClient, utility
        from pymilvus import AsyncMilvusClient

        try:
            # Parse connection string (format: "host:port")
            if hasattr(self.config, "location") and ":" in self.config.location:
                if self.config.location.startswith("http://"):
                    uri = self.config.location
                else:
                    uri = f"http://{self.config.location}"
            elif hasattr(self.config, "location"):
                uri = self.config.location
            else:
                uri = "http://localhost:19530"

            logger.info(f"Connecting to Milvus at {uri}")

            # Initialize MilvusClient (sync)
            self.client = MilvusClient(uri=uri)

            # Initialize AsyncMilvusClient (async)
            self.async_client = AsyncMilvusClient(uri=uri)

            # Ensure collection_name is set
            if not hasattr(self, "collection_name") or not self.collection_name:
                self.collection_name = getattr(
                    self.config, "collection_name", "default_collection"
                )

            # Ensure metric_type is set
            if not hasattr(self, "metric_type") or not self.metric_type:
                self.metric_type = getattr(self.config, "metric_type", "L2")

            # Ensure dim is set
            if not hasattr(self, "dim") or not self.dim:
                self.dim = getattr(self.config, "dim", 1024)  # Default dimension

            # Set default index params if not provided
            if not hasattr(self, "index_params") or not self.index_params:
                self.index_params = {
                    "metric_type": self.metric_type,
                    "index_type": "IVF_FLAT",
                    "params": {"nlist": 1024},
                }

            # Ensure metric_type is set in index_params
            if "metric_type" not in self.index_params:
                self.index_params["metric_type"] = self.metric_type

            logger.info(f"Checking collection: {self.collection_name}")

            # Check if collection exists, create if not
            self.create_collection()

            logger.info("Milvus client initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Milvus client: {str(e)}")
            raise

    def shutdown(self):
        """Clean up resources."""
        if self.client:
            self.client.close()
        self.client = None
        if self.async_client:
            asyncio.run(self.async_client.close())
        self.async_client = None

    def create_collection(
        self,
        collection_name: str = None,
        dim: int = None,
        metric_type: str = None,
        index_params: dict = None,
        **kwargs,
    ):
        """Create a new collection in the vector database."""
        try:
            collection_name = collection_name or self.collection_name
            metric_type = metric_type or self.metric_type
            index_params = index_params or self.index_params
            dim = dim or self.dim
            if not self.client.has_collection(collection_name):
                self.client.create_collection(
                    collection_name=collection_name,
                    dimension=dim,
                    primary_field_name="id",
                    id_type="str",
                    auto_id=False,
                    consistency_level="Strong",
                    metric_type=metric_type,
                    max_length=len(str(uuid.uuid4())) + 1,
                    index_params=index_params,
                    **kwargs,
                )
            self.client.load_collection(collection_name)
            return True

        except Exception as e:
            logger.error(f"Error creating collection {collection_name}: {str(e)}")
            raise ValueError("Collection not initialized") from e

    def drop_collection(self, collection_name: str):
        try:
            if self.client.has_collection(collection_name):
                self.client.drop_collection(collection_name)
        except Exception as e:
            logger.error(f"Error dropping collection {collection_name}: {str(e)}")
            raise ValueError("Collection not dropped") from e

    def store(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        """Insert vectors into the collection.

        Args:
            texts: List of texts to insert
            embeddings: List of embeddings to insert
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional arguments

        Returns:
            List of inserted IDs
        """
        try:
            collection_name = collection_name or self.collection_name
            self.create_collection(collection_name)
            if len(embeddings) == 0:
                return []
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in texts]
            texts = list(texts)
            data = [
                {
                    "id": ids[idx],
                    "vector": embedding,
                    "text": texts[idx],
                    "metadata": metadata,
                }
                for idx, (embedding, metadata) in enumerate(
                    zip(embeddings, metadatas or [{}] * len(embeddings))
                )
            ]
            insertion_size = kwargs.get("insertion_size", 6400000)
            embedding_dim = len(embeddings[0])
            batch_size = insertion_size // embedding_dim
            for i in range(0, len(data), batch_size):
                self.client.upsert(
                    collection_name=collection_name,
                    data=data[i : i + batch_size],
                )
            return ids
        except Exception as e:
            logger.error(
                f"Error storing data in collection {collection_name}: {str(e)}"
            )
            raise ValueError("Data not stored") from e

    def search(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        try:
            collection_name = collection_name or self.collection_name
            if not self.client.has_collection(collection_name):
                raise ValueError(f"Collection {collection_name} does not exist")
            results = self.client.search(
                collection_name=collection_name,
                data=query_embeddings,
                limit=k,
                search_params={"metric_type": self.metric_type, "params": {}},
                output_fields=["text", "metadata"],
                **kwargs,
            )
            # sort by distance in ascending order
            for result in results:
                result.sort(key=lambda x: x["distance"], reverse=True)
            return results
        except Exception as e:
            logger.error(f"Error searching collection {collection_name}: {str(e)}")
            raise ValueError("Search failed") from e

    def list_collections(self) -> List[str]:
        """List all collections in the database."""
        try:
            # MilvusClient uses list_collections() to get all collections
            return self.client.list_collections()
        except Exception as e:
            logger.error(f"Error listing collections: {str(e)}")
            return []

    async def list_collections_async(self) -> List[str]:
        """List all collections in the database asynchronously."""
        try:
            # AsyncMilvusClient do not has no list_collections method
            return self.client.list_collections()
        except Exception as e:
            logger.error(f"Error listing collections asynchronously: {str(e)}")
            return []

    async def create_collection_async(
        self,
        collection_name: str = None,
        dim: int = None,
        metric_type: str = None,
        index_params: dict = None,
        **kwargs,
    ):
        """Create a new collection in the vector database asynchronously."""
        try:
            collection_name = collection_name or self.collection_name
            metric_type = metric_type or self.metric_type
            index_params = index_params or self.index_params
            dim = dim or self.dim
            has_collection = self.client.has_collection(collection_name)
            if not has_collection:
                await self.async_client.create_collection(
                    collection_name=collection_name,
                    dimension=dim,
                    primary_field_name="id",
                    id_type="str",
                    auto_id=False,
                    consistency_level="Strong",
                    metric_type=metric_type,
                    max_length=len(str(uuid.uuid4())) + 1,
                    index_params=index_params,
                    **kwargs,
                )
            await self.async_client.load_collection(collection_name)
            return True

        except Exception as e:
            logger.error(
                f"Error creating collection asynchronously {collection_name}: {str(e)}"
            )
            raise ValueError("Collection not initialized") from e

    async def drop_collection_async(self, collection_name: str):
        """Drop a collection asynchronously."""
        try:
            has_collection = self.client.has_collection(collection_name)
            if has_collection:
                await self.async_client.drop_collection(collection_name)
            return True
        except Exception as e:
            logger.error(
                f"Error dropping collection asynchronously {collection_name}: {str(e)}"
            )
            raise ValueError("Collection not dropped") from e

    async def store_async(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        """Insert vectors into the collection asynchronously.

        Args:
            texts: List of texts to insert
            embeddings: List of embeddings to insert
            metadatas: Optional list of metadata dictionaries
            ids: Optional list of IDs
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional arguments

        Returns:
            List of inserted IDs
        """
        try:
            collection_name = collection_name or self.collection_name
            await self.create_collection_async(collection_name)
            if len(embeddings) == 0:
                return []
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in texts]
            texts = list(texts)
            data = [
                {
                    "id": ids[idx],
                    "vector": embedding,
                    "text": texts[idx],
                    "metadata": metadata,
                }
                for idx, (embedding, metadata) in enumerate(
                    zip(embeddings, metadatas or [{}] * len(embeddings))
                )
            ]
            insertion_size = kwargs.get("insertion_size", 6400000)
            embedding_dim = len(embeddings[0])
            batch_size = insertion_size // embedding_dim
            tasks = []
            for i in range(0, len(data), batch_size):
                tasks.append(
                    self.async_client.upsert(
                        collection_name=collection_name,
                        data=data[i : i + batch_size],
                    )
                )
            await asyncio.gather(*tasks)
            return ids
        except Exception as e:
            logger.error(
                f"Error storing data asynchronously in collection {collection_name}: {str(e)}"
            )
            raise ValueError("Data not stored") from e

    async def search_async(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        """Search for similar vectors in the collection asynchronously.

        Args:
            query_texts: List of query texts
            query_embeddings: List of query embeddings
            k: Number of results to return per query
            collection_name: Optional collection name (uses instance collection_name if not provided)
            **kwargs: Additional search parameters

        Returns:
            List of lists of search results (one list per query)
        """
        try:
            collection_name = collection_name or self.collection_name
            has_collection = self.client.has_collection(collection_name)
            if not has_collection:
                raise ValueError(f"Collection {collection_name} does not exist")
            results = await self.async_client.search(
                collection_name=collection_name,
                data=query_embeddings,
                limit=k,
                search_params={"metric_type": self.metric_type, "params": {}},
                output_fields=["text", "metadata"],
                **kwargs,
            )
            # sort by distance in ascending order
            for result in results:
                result.sort(key=lambda x: x["distance"], reverse=True)
            return results
        except Exception as e:
            logger.error(
                f"Error searching collection asynchronously {collection_name}: {str(e)}"
            )
            raise ValueError("Search failed") from e


class MilvusLiteServiceInstance(MilvusServiceInstance):
    """VectorDB service implementation for Milvus Lite.

    This is a thin wrapper around MilvusServiceInstance.
    The difference between MilvusServiceInstance and MilvusLiteServiceInstance is that
    MilvusLiteServiceInstance uses a local Milvus server.
    """

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: VectorDBServiceInstanceConfig,
    ):
        # Initialize base class first
        super().__init__(instance_name, group_name, config)
        self.server = None

    def launch(self):
        """Initialize Milvus Lite. The uri should be a filename."""
        assert self.config.location is not None
        assert not self.config.location.startswith("http://")
        assert ":" not in self.config.location
        super().launch()

    # The rest of the methods are the same as MilvusServiceInstance
    # since they both use the same pymilvus API


class SimpleVectorDB:
    """A simple in-memory vector database for classification examples."""

    texts: List[str]
    vectors: List[List[float]]
    metadata: List[Dict[str, Any]]

    def __init__(
        self,
        dim: int,
        texts: List[str] = [],
        vectors: List[List[float]] = [],
        metadata: List[Dict[str, Any]] = [],
    ):
        self.dim = dim
        self.texts = texts
        self.vectors = vectors
        self.metadata = metadata

    def add(
        self,
        texts: List[str],
        embeddings: List[List[float]],
        metadatas: Optional[List[Dict[str, Any]]] = None,
    ):
        """Add examples to the vector database.

        Args:
            texts: List of text examples
            embeddings: List of embeddings for the texts
            metadatas: Optional list of metadata for each example
        """
        if len(texts) != len(embeddings):
            raise ValueError("Number of texts and embeddings must match")
        if metadatas is None:
            metadatas = [{}] * len(texts)
        self.texts.extend(texts)
        self.vectors.extend(embeddings)
        self.metadata.extend(metadatas)

    def search(
        self, query_embeddings: List[List[float]], k: int = 5
    ) -> List[List[Dict[str, Any]]]:
        """Search for similar examples in the vector database.

        Args:
            query_embeddings: The query embeddings
            k: Number of results to return

        Returns:
            List of similar examples with metadata
        """
        if not self.vectors:
            logger.warning("Vector database is empty")
            return []

        # Convert to numpy arrays for efficient computation
        query_embeddings = np.array(query_embeddings)
        vectors = np.array(self.vectors)

        # Compute cosine similarity
        similarities = np.dot(vectors, query_embeddings.T) / (
            np.linalg.norm(vectors, axis=1)[:, np.newaxis]
            * np.linalg.norm(query_embeddings, axis=1)
        )

        # Get the indices of the top k results
        top_indices = np.argsort(similarities, axis=0)[-k:][::-1]

        # Return the top k results with metadata
        return [
            [
                {
                    "text": self.texts[idx],
                    "embedding": self.vectors[idx],
                    "metadata": self.metadata[idx],
                    "score": float(similarities[idx]),
                }
                for idx in top_indices
            ]
            for top_indices in top_indices
        ]

    async def add_async(
        self,
        texts: List[str],
        embeddings: List[List[float]],
        metadatas: Optional[List[Dict[str, Any]]] = None,
    ):
        """Add examples to the vector database asynchronously."""
        self.add(texts, embeddings, metadatas)

    async def search_async(
        self,
        query_embeddings: List[List[float]],
        k: int = 5,
    ) -> List[List[Dict[str, Any]]]:
        """Search for similar examples in the vector database asynchronously."""
        return self.search(query_embeddings, k)


class SimpleVectorDBServiceInstance(VectorDBServiceInstance):
    """VectorDB service implementation for a simple in-memory vector database."""

    def __init__(
        self,
        instance_name: str,
        group_name: str,
        config: VectorDBServiceInstanceConfig,
    ):
        super().__init__(instance_name, group_name, config)
        self.collections: Dict[str, SimpleVectorDB] = {}

    @override
    def launch(self):
        try:
            # Ensure collection_name is set
            if not hasattr(self, "collection_name") or not self.collection_name:
                self.collection_name = getattr(
                    self.config, "collection_name", "default_collection"
                )

            # Ensure metric_type is set
            if not hasattr(self, "metric_type") or not self.metric_type:
                self.metric_type = getattr(self.config, "metric_type", "L2")

            # Ensure dim is set
            if not hasattr(self, "dim") or not self.dim:
                self.dim = getattr(self.config, "dim", 1024)  # Default dimension

            self.create_collection()

        except Exception as e:
            logger.error(f"Failed to initialize SimpleVectorDB: {str(e)}")
            raise ValueError("SimpleVectorDB not initialized") from e

    def shutdown(self):
        self.collections.clear()

    def has_collection(self, collection_name: str):
        return collection_name in self.collections

    def create_collection(self, collection_name: str = None, dim: int = None):
        try:
            collection_name = collection_name or self.collection_name
            dim = dim or self.dim
            if self.has_collection(collection_name):
                return
            self.collections[collection_name] = SimpleVectorDB(dim)
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {str(e)}")
            raise ValueError("Collection not created") from e

    def drop_collection(self, collection_name: str):
        try:
            collection_name = collection_name or self.collection_name
            if self.has_collection(collection_name):
                del self.collections[collection_name]
        except Exception as e:
            logger.error(f"Failed to drop collection {collection_name}: {str(e)}")
            raise ValueError("Collection not dropped") from e

    def store(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        try:
            collection_name = collection_name or self.collection_name
            self.create_collection(collection_name)
            if not isinstance(texts, list):
                texts = list(texts)
            self.collections[collection_name].add(texts, embeddings, metadatas)
        except Exception as e:
            logger.error(
                f"Failed to store texts in collection {collection_name}: {str(e)}"
            )
            raise ValueError("Texts not stored") from e

    def search(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        try:
            collection_name = collection_name or self.collection_name
            self.create_collection(collection_name)
            return self.collections[collection_name].search(query_embeddings, k)
        except Exception as e:
            logger.error(f"Failed to search in collection {collection_name}: {str(e)}")
            raise ValueError("Search failed") from e

    async def create_collection_async(
        self, collection_name: str, dim: int, **kwargs
    ) -> bool:
        try:
            self.create_collection(collection_name, dim, **kwargs)
            return True
        except Exception as e:
            logger.error(f"Failed to create collection {collection_name}: {str(e)}")
            raise ValueError("Collection not created") from e

    async def drop_collection_async(self, collection_name: str, **kwargs) -> bool:
        try:
            self.drop_collection(collection_name, **kwargs)
            return True
        except Exception as e:
            logger.error(f"Failed to drop collection {collection_name}: {str(e)}")
            raise ValueError("Collection not dropped") from e

    async def store_async(
        self,
        texts: Iterable[str],
        embeddings: list[list[float]],
        metadatas: Optional[List[dict]] = None,
        ids: Optional[List[str]] = None,
        collection_name: str = None,
        **kwargs,
    ) -> List[str]:
        try:
            collection_name = collection_name or self.collection_name
            self.create_collection(collection_name)
            if not isinstance(texts, list):
                texts = list(texts)
            await self.collections[collection_name].add_async(
                texts, embeddings, metadatas
            )
        except Exception as e:
            logger.error(
                f"Failed to store texts in collection {collection_name}: {str(e)}"
            )
            raise ValueError("Texts not stored") from e

    async def search_async(
        self,
        query_texts: Iterable[str],
        query_embeddings: list[list[float]],
        k: int = 5,
        collection_name: str = None,
        **kwargs,
    ) -> List[List[Dict]]:
        try:
            collection_name = collection_name or self.collection_name
            self.create_collection(collection_name)
            return await self.collections[collection_name].search_async(
                query_embeddings, k
            )
        except Exception as e:
            logger.error(f"Failed to search in collection {collection_name}: {str(e)}")
            raise ValueError("Search failed") from e

    def list_collections(self):
        return self.collections.keys()


class VectorDBService(BaseService):
    """VectorDB service for managing vector database instances."""

    def __init__(self, scheduling_policy: str = "round_robin"):
        super().__init__("vdb", scheduling_policy)

    def clear_all(self):
        """Clear all collections in all instances."""
        for instance in self.instances.values():
            collections = list(instance.list_collections())
            for collection in collections:
                instance.drop_collection(collection)
                logger.info("Dropped collection %s", collection)
