# LegoAgent Testing Framework

This document describes the comprehensive testing framework for legoAgent, designed to provide robust testing capabilities across different test types and scenarios.

## Overview

The testing framework is organized into four main categories:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test interactions between components  
- **Functional Tests**: Test end-to-end workflows and user scenarios
- **Performance Tests**: Benchmark and measure system performance

## Directory Structure

```
tests/
├── README.md                          # This file
├── conftest.py                        # Global pytest configuration
├── base.py                           # Base test classes and utilities
├── fixtures/                         # Shared test fixtures
│   ├── __init__.py
│   ├── common.py                     # Common test data and fixtures
│   ├── services.py                   # Service-related fixtures
│   └── workflows.py                  # Workflow-related fixtures
├── mocks/                            # Mock services and utilities
│   ├── __init__.py
│   ├── services.py                   # Mock service implementations
│   └── factories.py                  # Test data factories
├── unit/                             # Unit tests
│   ├── test_services.py          # Example unit tests
│   └── ...
├── integration/                      # Integration tests
│   ├── test_service_manager_integration.py
│   └── ...
├── functional/                       # Functional/E2E tests
│   ├── test_workflow_end_to_end.py
│   └── ...
└── performance/                      # Performance tests
    ├── test_service_performance.py
    └── ...
```

## Test Categories

### Unit Tests (`tests/unit/`)

Unit tests focus on testing individual components in isolation with all dependencies mocked.

**Characteristics:**
- Fast execution (< 100ms per test)
- Complete isolation using mocks
- High test coverage of individual functions/classes
- No external dependencies

**Example:**
```python
@pytest.mark.unit
class TestLLMService(UnitTestCase):
    def test_llm_service_basic_request(self):
        service = MockLLMService(responses=["Test response"])
        request = LLMServiceRequestFactory()
        response = service.serve(request)
        assert response is not None
```

### Integration Tests (`tests/integration/`)

Integration tests verify interactions between components with selective mocking.

**Characteristics:**
- Test component interactions
- Mix of real and mock services
- Moderate execution time (< 1s per test)
- Focus on service integration

**Example:**
```python
@pytest.mark.integration
class TestServiceManagerIntegration(IntegrationTestCase):
    def test_service_manager_service_requests(self):
        # Test real service manager with mock services
        llm = self.service_manager.get_service("llm")
        response = llm.serve(request)
        assert response is not None
```

### Functional Tests (`tests/functional/`)

Functional tests verify complete workflows and user scenarios end-to-end.

**Characteristics:**
- End-to-end workflow testing
- Minimal mocking
- Realistic scenarios
- Longer execution time (< 10s per test)

**Example:**
```python
@pytest.mark.functional
class TestChainWorkflowEndToEnd(FunctionalTestCase):
    def test_chain_workflow_complete_scenario(self):
        # Test complete chain workflow from start to finish
        response = chain(request)
        assert len(response.steps) == len(request.prompts)
```

### Performance Tests (`tests/performance/`)

Performance tests benchmark and measure system performance characteristics.

**Characteristics:**
- Benchmarking and profiling
- Performance regression detection
- Load testing
- Memory usage monitoring

**Example:**
```python
@pytest.mark.performance
class TestServicePerformance(PerformanceTestCase):
    @pytest.mark.benchmark
    def test_llm_service_throughput(self, benchmark):
        result = benchmark(lambda: service.serve(request))
        assert benchmark.stats.mean < 0.01
```

## Base Test Classes

The framework provides base classes for different test types:

### `UnitTestCase`
- Comprehensive mocking setup
- Fast execution utilities
- Isolated testing environment

### `IntegrationTestCase`
- Selective service mocking
- Component interaction testing
- Service manager setup

### `FunctionalTestCase`
- Minimal mocking
- End-to-end testing utilities
- Realistic test environment

### `PerformanceTestCase`
- Performance monitoring
- Metrics collection
- Benchmark utilities

### `AsyncTestCase`
- Async testing utilities
- Event loop management
- Async mock support

## Mock Services

The framework provides comprehensive mock services:

### `MockLLMService`
```python
service = MockLLMService(
    responses=["Response 1", "Response 2"],
    delay=0.1  # Simulate response time
)
```

### `MockEmbeddingService`
```python
service = MockEmbeddingService(
    embedding_dim=384,
    delay=0.05
)
```

### `MockVectorDBService`
```python
service = MockVectorDBService(delay=0.02)
```

## Test Fixtures

### Common Fixtures (`tests/fixtures/common.py`)
- `sample_text_data`: Sample text for testing
- `sample_embeddings`: Sample embedding vectors
- `sample_chat_messages`: Sample chat conversations
- `test_config_data`: Test configuration data

### Service Fixtures (`tests/fixtures/services.py`)
- `mock_llm_service`: Configured LLM service
- `mock_embedding_service`: Configured embedding service
- `configured_service_manager`: Service manager with all services

### Workflow Fixtures (`tests/fixtures/workflows.py`)
- `chain_workflow_data`: Chain workflow test data
- `parallel_workflow_data`: Parallel workflow test data
- `route_workflow_data`: Route workflow test data

## Test Factories

Test factories generate realistic test data:

```python
# Generate test requests
llm_request = LLMServiceRequestFactory()
embedding_request = EmbeddingServiceRequestFactory()

# Generate workflow requests
chain_request = ChainWorkflowRequestFactory()
parallel_request = ParallelWorkflowRequestFactory()

# Generate mock responses
llm_response = LLMResponseFactory()
embeddings = EmbeddingResponseFactory.create(num_embeddings=5)
```

## Running Tests

### Basic Test Execution
```bash
# Run all tests
./run_tests.sh

# Run specific test types
pytest -m unit                    # Unit tests only
pytest -m integration             # Integration tests only
pytest -m functional              # Functional tests only
pytest -m performance             # Performance tests only
```

### Advanced Test Execution
```bash
# Run with coverage
pytest --cov=src --cov-report=html

# Run performance benchmarks
pytest tests/performance/ --benchmark-only

# Run async tests only
pytest -m async_test

# Run excluding slow tests
pytest -m 'not slow'

# Run specific test file
pytest tests/unit/test_services.py -v
```

### Parallel Test Execution
```bash
# Run tests in parallel
pytest -n auto  # Auto-detect CPU cores
pytest -n 4     # Use 4 workers
```

## Test Markers

The framework uses pytest markers to categorize tests:

- `@pytest.mark.unit`: Unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.functional`: Functional tests
- `@pytest.mark.performance`: Performance tests
- `@pytest.mark.slow`: Slow-running tests
- `@pytest.mark.async_test`: Async tests
- `@pytest.mark.benchmark`: Benchmark tests

## Performance Testing

### Benchmarking
```python
@pytest.mark.benchmark
def test_function_performance(benchmark):
    result = benchmark(function_to_test)
    assert benchmark.stats.mean < threshold
```

### Performance Tracking
```python
def test_with_performance_tracking(performance_tracker):
    performance_tracker.start_timer("operation")
    # ... perform operation
    performance_tracker.end_timer("operation")
    performance_tracker.assert_under_threshold("operation", 1.0)
```

### Load Testing
```python
def test_concurrent_load():
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request) for _ in range(100)]
        results = [future.result() for future in as_completed(futures)]
    assert len(results) == 100
```

## Best Practices

### Test Organization
1. Use appropriate test categories (unit/integration/functional/performance)
2. Follow naming conventions (`test_*.py`, `Test*` classes)
3. Use descriptive test names that explain what is being tested

### Mock Usage
1. Use comprehensive mocks for unit tests
2. Use selective mocks for integration tests
3. Minimize mocks for functional tests
4. Reset mocks between tests

### Performance Testing
1. Use benchmarks for critical performance paths
2. Set realistic performance thresholds
3. Monitor memory usage in long-running tests
4. Test both sync and async code paths

### Async Testing
1. Use `@pytest.mark.asyncio` for async tests
2. Properly handle event loops
3. Test concurrent operations
4. Verify async performance characteristics

## Configuration

### pytest.ini Configuration
The framework is configured in `pyproject.toml`:

```toml
[tool.pytest.ini_options]
addopts = "-v --cov=src --cov-report=term-missing --cov-report=html --strict-markers"
markers = [
    "unit: Unit tests for individual components",
    "integration: Integration tests for component interactions",
    "functional: End-to-end functional tests",
    "performance: Performance and benchmark tests",
    "slow: Tests that take a long time to run",
    "async_test: Async tests requiring special handling",
]
```

### Coverage Configuration
Coverage is configured to:
- Include all source code in `src/`
- Generate HTML reports
- Show missing lines
- Use branch coverage

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `src/` is in Python path
2. **Async Test Failures**: Check event loop configuration
3. **Mock Issues**: Verify mock setup and reset between tests
4. **Performance Test Failures**: Adjust thresholds for your environment

### Debug Mode
```bash
# Run with verbose output
pytest -v -s

# Run with debug logging
pytest --log-cli-level=DEBUG

# Run single test with debugging
pytest tests/unit/test_services.py::TestLLMService::test_llm_service_basic_request -v -s
```

This testing framework provides a solid foundation for maintaining high code quality and ensuring reliable operation of the legoAgent system.
