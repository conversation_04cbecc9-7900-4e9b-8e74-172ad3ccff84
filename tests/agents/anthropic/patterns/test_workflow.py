"""Tests for basic agent workflow functionality."""

import pytest
from lego_agent.agents.anthropic.patterns.workflow.routing import (
    RouteWorkflowRequest,
    RouteWorkflowResponse,
    route,
    route_async,
)
from lego_agent.agents.anthropic.patterns.workflow.paralleling import (
    ParallelWorkflowRequest,
    ParallelWorkflowResponse,
    parallel,
    parallel_async,
)
from lego_agent.agents.anthropic.patterns.workflow.chaining import (
    ChainWorkflowRequest,
    ChainWorkflowResponse,
    chain,
    chain_async,
)
from lego_agent.agents.anthropic.patterns.workflow.looping import (
    LoopWorkflowRequest,
    LoopWorkflowResponse,
    loop,
    loop_async,
)
from lego_agent.agents.anthropic.patterns.workflow.planner_workers import (
    PlannerWorkersRequest,
    PlannerWorkersResponse,
    process,
    process_async,
    _get_planner_prompt,
    _get_worker_prompt,
    _parse_tasks,
)
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    LLMTaskRequest,
    llm_call,
)


@pytest.fixture
def support_routes():
    """Test fixture for support routing prompts."""
    return {
        "billing": """You are a billing support specialist. Follow these guidelines:
        1. Always start with "Billing Support Response:"
        2. First acknowledge the specific billing issue
        3. Explain any charges or discrepancies clearly
        4. List concrete next steps with timeline
        5. End with payment options if relevant

        Keep responses professional but friendly.""",
        "technical": """You are a technical support engineer. Follow these guidelines:
        1. Always start with "Technical Support Response:"
        2. List exact steps to resolve the issue
        3. Include system requirements if relevant
        4. Provide workarounds for common problems
        5. End with escalation path if needed

        Use clear, numbered steps and technical details.""",
        "account": """You are an account security specialist. Follow these guidelines:
        1. Always start with "Account Support Response:"
        2. Prioritize account security and verification
        3. Provide clear steps for account recovery/changes
        4. Include security tips and warnings
        5. Set clear expectations for resolution time

        Maintain a serious, security-focused tone.""",
    }


@pytest.fixture
def test_tickets():
    """Test fixture for support tickets."""
    return [
        """Subject: Can't access my account
        Message: Hi, I've been trying to log in for the past hour but keep getting an 'invalid password' error.
        I'm sure I'm using the right password. Can you help me regain access?""",
        """Subject: Unexpected charge on my card
        Message: Hello, I just noticed a charge of $49.99 on my credit card from your company.""",
    ]


@pytest.fixture
def stakeholders():
    """Test fixture for stakeholder analysis."""
    return [
        """Customers:
        - Price sensitive
        - Want better tech
        - Environmental concerns""",
        """Employees:
        - Job security worries
        - Need new skills
        - Want clear direction""",
    ]


@pytest.fixture
def data_processing_steps():
    """Test fixture for data processing chain steps."""
    return [
        """Extract only the numerical values and their associated metrics from the text.
        Format each as 'value: metric' on a new line.""",
        """Convert all numerical values to percentages where possible.
        If not a percentage or points, convert to decimal (e.g., 92 points -> 92%).""",
        """Sort all lines in descending order by numerical value.""",
        """Format the sorted data as a markdown table with columns:
        | Metric | Value |""",
    ]


class TestWorkflow:
    """Test suite for workflow functionality."""

    def test_route_returns_response_for_each_ticket(self, support_routes, test_tickets):
        """Test that route function returns appropriate responses for different tickets."""
        for ticket in test_tickets:
            request = RouteWorkflowRequest(input=ticket, routes=support_routes)
            response = route(request)
            assert isinstance(response, RouteWorkflowResponse)
            assert isinstance(response.output, str)
            assert len(response.output) > 0
            # Response should start with one of the support team identifiers
            assert any(
                response.output.strip().startswith(f"{team.title()} Support Response:")
                for team in support_routes.keys()
            )

    @pytest.mark.asyncio
    async def test_route_async_returns_response_for_each_ticket(
        self, support_routes, test_tickets
    ):
        """Test that route_async function returns appropriate responses for different tickets."""
        for ticket in test_tickets:
            request = RouteWorkflowRequest(input=ticket, routes=support_routes)
            response = await route_async(request)
            assert isinstance(response, RouteWorkflowResponse)
            assert isinstance(response.output, str)
            assert len(response.output) > 0
            assert any(
                response.output.strip().startswith(f"{team.title()} Support Response:")
                for team in support_routes.keys()
            )

    def test_parallel_processes_multiple_inputs(self, stakeholders):
        """Test that parallel function processes multiple inputs concurrently."""
        prompt = "Analyze how market changes will impact this stakeholder group."
        request = ParallelWorkflowRequest(
            prompt=prompt, inputs=stakeholders, n_workers=2
        )
        response = parallel(request)

        assert isinstance(response, ParallelWorkflowResponse)
        assert isinstance(response.outputs, list)
        assert len(response.outputs) == len(stakeholders)
        assert all(isinstance(output, str) for output in response.outputs)
        assert all(len(output) > 0 for output in response.outputs)

    @pytest.mark.asyncio
    async def test_parallel_async_processes_multiple_inputs(self, stakeholders):
        """Test that parallel_async function processes multiple inputs concurrently."""
        prompt = "Analyze how market changes will impact this stakeholder group."
        request = ParallelWorkflowRequest(prompt=prompt, inputs=stakeholders)
        response = await parallel_async(request)

        assert isinstance(response, ParallelWorkflowResponse)
        assert isinstance(response.outputs, list)
        assert len(response.outputs) == len(stakeholders)
        assert all(isinstance(output, str) for output in response.outputs)
        assert all(len(output) > 0 for output in response.outputs)

    def test_chain_transforms_data_sequentially(self, data_processing_steps):
        """Test that chain function processes data through multiple steps."""
        input_text = """
        Q3 Performance Summary:
        Customer satisfaction score: 92 points
        Revenue growth: 45%
        Market share: 23%
        """

        request = ChainWorkflowRequest(input=input_text, prompts=data_processing_steps)
        response: ChainWorkflowResponse = chain(request=request)
        result: str = response.output

        assert isinstance(result, str)
        assert len(result) > 0
        assert result.count("|") == 15  # Should be formatted as markdown table
        assert "45" in result  # Should contain decimal values
        assert "23" in result  # Should contain decimal values
        assert "92" in result  # Should preserve points unit

    @pytest.mark.asyncio
    async def test_chain_async_transforms_data_sequentially(
        self, data_processing_steps
    ):
        """Test that chain_async function processes data through multiple steps."""
        input_text = """
        Q3 Performance Summary:
        Customer satisfaction score: 92 points
        Revenue growth: 45%
        Market share: 23%
        """

        request = ChainWorkflowRequest(input=input_text, prompts=data_processing_steps)
        response: ChainWorkflowResponse = await chain_async(request=request)
        result = response.output

        assert isinstance(result, str)
        assert len(result) > 0
        assert result.count("|") == 15  # Should be formatted as markdown table
        assert "45" in result  # Should contain decimal values
        assert "23" in result  # Should contain decimal values
        assert "92" in result  # Should preserve points unit

    def test_loop_generates_valid_solution(self):
        """Test that loop function generates a valid solution that passes evaluation."""
        task = """
            <user input>
            Implement a Stack with:
            1. push(x)
            2. pop()
            3. getMin()
            All operations should be O(1).
            </user input>
            """
        request = LoopWorkflowRequest(task=task, max_iterations=3)
        response = loop(request)

        assert response is not None
        assert isinstance(response, LoopWorkflowResponse)
        assert response.result is not None
        assert response.steps is not None
        assert len(response.steps) > 0
        assert all(step.evaluation is not None for step in response.steps)

    @pytest.mark.asyncio
    async def test_loop_async_generates_valid_solution(self):
        """Test that loop_async function generates a valid solution that passes evaluation."""
        task = """
            <user input>
            Implement a Stack with:
            1. push(x)
            2. pop()
            3. getMin()
            All operations should be O(1).
            </user input>
            """
        request = LoopWorkflowRequest(task=task, max_iterations=3)
        response = await loop_async(request)

        assert response is not None
        assert isinstance(response, LoopWorkflowResponse)
        assert response.result is not None
        assert response.steps is not None
        assert len(response.steps) > 0
        assert all(step.evaluation is not None for step in response.steps)

    def test_process_creates_and_executes_subtasks(self):
        """Test that process function creates subtasks and executes them."""
        task = "Write a product description for a new eco-friendly water bottle"
        request = PlannerWorkersRequest(task=task, max_subtasks=2)

        # Run the process
        response = process(request)

        # Print the response for debugging
        print("\n=== PLANNER RESPONSE ===")
        print(f"Analysis: {response.analysis}")
        print(f"Number of worker results: {len(response.worker_results)}")
        for i, result in enumerate(response.worker_results, 1):
            print(f"\nWorker {i} (Type: {result.type}):")
            print(f"Description: {result.description}")
            print(f"Result: {result.result}")

        # Basic response validation
        assert isinstance(response, PlannerWorkersResponse)
        assert isinstance(response.analysis, str)
        assert len(response.analysis) > 0
        assert isinstance(response.worker_results, list)

        # If there are worker results, validate their structure
        for result in response.worker_results:
            assert hasattr(result, "type")
            assert hasattr(result, "description")
            assert hasattr(result, "result")
            if result.result:  # Only check non-empty results
                assert isinstance(result.result, str)

    @pytest.mark.asyncio
    async def test_process_async_creates_and_executes_subtasks(self):
        """Test that process_async function creates subtasks and executes them."""
        task = "Write a product description for a new eco-friendly water bottle"
        request = PlannerWorkersRequest(task=task, max_subtasks=2)
        response = await process_async(request)

        # Basic response validation
        assert isinstance(response, PlannerWorkersResponse)
        assert isinstance(response.analysis, str)
        assert len(response.analysis) > 0
        assert isinstance(response.worker_results, list)

        # If there are worker results, validate their structure
        for result in response.worker_results:
            assert hasattr(result, "type")
            assert hasattr(result, "description")
            assert hasattr(result, "result")
            if result.result:  # Only check non-empty results
                assert isinstance(result.result, str)
