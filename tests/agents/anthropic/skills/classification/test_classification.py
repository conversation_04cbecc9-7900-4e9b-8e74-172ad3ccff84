"""Tests for classification workflow functionality."""

import asyncio
import logging
import pytest
from typing import List, Dict, Any

from lego_agent.core.registry_setup import get_service_manager
from lego_agent.core.service.vdb import VectorDBServiceRequest
from lego_agent.core.service.embedding import EmbeddingServiceRequest
from lego_agent.core.config import config_manager

# Import workflow components
from lego_agent.agents.anthropic.skills.classification.workflow.rag_chain_of_thought import (
    RAGChainOfThoughtWorkflowRequest,
    rag_chain_of_thought_classify,
    rag_chain_of_thought_classify_async,
)

# Import task components for finer-grained control
from lego_agent.agents.anthropic.skills.classification.task.embedding_task import (
    EmbeddingTaskRequest,
    embed_texts,
)
from lego_agent.agents.anthropic.skills.classification.task.vdb_task import (
    VDBSearchRequest,
    search_vdb,
)
from lego_agent.agents.anthropic.skills.classification.task.llm_task import (
    ClassificationLLMRequest,
    classify_with_llm,
)
from lego_agent.agents.anthropic.skills.classification.function.transform import (
    extract_classification_response,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def get_config_value(key, default=None):
    """Get a configuration value from the classification config."""
    return config_manager.get_config_value("anthropic.classification", key, default)


@pytest.fixture(scope="module")
def categories():
    """Fixture that provides the list of categories for classification."""
    return [
        "Coverage Explanations",
        "Billing Inquiries",
        "Account Management",
        "Billing Disputes",
        "Policy Administration",
        "Claims Assistance",
        "Claims Disputes",
        "Quotes and Proposals",
        "Policy Comparisons",
        "General Inquiries",
    ]


@pytest.fixture(scope="module")
def training_texts():
    """Fixture that provides example training texts."""
    return [
        "Can you explain what my health insurance covers for preventive care?",
        "I need to update my billing information for my auto insurance.",
        "How do I add my spouse to my existing policy?",
        "I was charged twice for my premium this month.",
        "I need to make changes to my policy coverage.",
        "I need help filing a claim for a recent car accident.",
        "My claim was denied and I don't understand why.",
        "Can I get a quote for home insurance?",
        "How does your premium plan compare to your basic plan?",
        "What are your customer service hours?",
    ]


@pytest.fixture(scope="module")
def test_texts():
    """Fixture that provides example test texts for classification."""
    return [
        "I don't understand why my dental visit wasn't covered by my insurance.",
        "I want to dispute a charge on my latest bill.",
        "Can you help me understand the different coverage options for home insurance?",
    ]


@pytest.fixture(scope="function")
def vector_db_setup(categories, training_texts):
    """Fixture to set up the vector database with test data."""
    # Get service manager
    service_manager = get_service_manager()

    # Get services
    embedding_service = service_manager.get_service("embedding")
    vdb_service = service_manager.get_service("vdb")

    # Generate embeddings for training texts
    embedding_request = EmbeddingServiceRequest(
        service_name="embedding",
        operation_name="embed",
        raw_data={
            "texts": training_texts,
            "model": get_config_value("embedding_model", "BAAI/bge-reranker-large"),
        },
    )
    embedding_response = embedding_service.serve(embedding_request)
    embeddings = embedding_response.raw_data.get("embeddings", [])

    if not embeddings:
        pytest.fail("Failed to generate embeddings for training data")

    # Create metadata for training data
    metadatas = [
        {"text": text, "label": category}
        for text, category in zip(training_texts, categories)
    ]

    # Store training data in vector database
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="store",
        raw_data={
            "texts": training_texts,
            "embeddings": embeddings,
            "metadatas": metadatas,
            "collection_name": get_config_value("collection_name", "simple"),
        },
    )
    vdb_service.serve(vdb_request)

    logger.info(f"Added {len(training_texts)} examples to the vector database")

    yield  # This is where the test runs

    # Teardown - clear the vector database
    vdb_service.clear_all()
    logger.info("Cleared vector database")


@pytest.fixture(scope="function")
async def vector_db_setup_async(categories, training_texts):
    """Async fixture to set up the vector database with test data."""
    # Get service manager
    service_manager = get_service_manager()

    # Get services
    embedding_service = service_manager.get_service("embedding")
    vdb_service = service_manager.get_service("vdb")

    # Generate embeddings for training texts
    embedding_request = EmbeddingServiceRequest(
        service_name="embedding",
        operation_name="embed",
        raw_data={
            "texts": training_texts,
            "model": get_config_value("embedding_model", "BAAI/bge-reranker-large"),
        },
    )
    embedding_response = await embedding_service.serve_async(embedding_request)
    embeddings = embedding_response.raw_data.get("embeddings", [])

    if not embeddings:
        pytest.fail("Failed to generate embeddings for training data")

    # Create metadata for training data
    metadatas = [
        {"text": text, "label": category}
        for text, category in zip(training_texts, categories)
    ]

    # Store training data in vector database
    vdb_request = VectorDBServiceRequest(
        service_name="vdb",
        operation_name="store_async",
        raw_data={
            "texts": training_texts,
            "embeddings": embeddings,
            "metadatas": metadatas,
            "collection_name": get_config_value("collection_name", "simple"),
        },
    )
    await vdb_service.serve_async(vdb_request)

    logger.info(f"Added {len(training_texts)} examples to the vector database")

    yield  # This is where the test runs

    # Teardown - clear the vector database
    vdb_service.clear_all()
    logger.info("Cleared vector database")


class TestClassificationWorkflow:
    """Test suite for classification workflow functionality."""

    def test_classification_workflow(self, vector_db_setup, categories, test_texts):
        """Test the classification workflow synchronously."""
        for text in test_texts:
            logger.info(f"Classifying: {text}")

            # Create workflow request
            request = RAGChainOfThoughtWorkflowRequest(
                text=text,
                categories=categories,
                collection_name=get_config_value("collection_name", "simple"),
                llm_model=get_config_value("llm_model", "Qwen/Qwen2.5-1.5B-Instruct"),
                embedding_model=get_config_value(
                    "embedding_model", "BAAI/bge-reranker-large"
                ),
                max_tokens=get_config_value("max_tokens", 4096),
                temperature=get_config_value("temperature", 0.0),
            )

            # Perform classification
            response = rag_chain_of_thought_classify(request)

            # Assertions
            assert response is not None
            assert hasattr(response, "category")
            assert response.category in categories
            assert hasattr(response, "reasoning")
            assert isinstance(response.reasoning, str)
            assert len(response.reasoning) > 0

    @pytest.mark.asyncio
    async def test_classification_workflow_async(
        self, vector_db_setup_async, categories, test_texts
    ):
        """Test the classification workflow asynchronously."""
        tasks = []
        for text in test_texts:
            logger.info(f"Classifying asynchronously: {text}")

            # Create workflow request
            request = RAGChainOfThoughtWorkflowRequest(
                text=text,
                categories=categories,
                collection_name=get_config_value("collection_name", "simple"),
                llm_model=get_config_value("llm_model", "Qwen/Qwen2.5-1.5B-Instruct"),
                embedding_model=get_config_value(
                    "embedding_model", "BAAI/bge-reranker-large"
                ),
                max_tokens=get_config_value("max_tokens", 4096),
                temperature=get_config_value("temperature", 0.0),
            )

            # Add task to list
            tasks.append(rag_chain_of_thought_classify_async(request))

        # Wait for all tasks to complete
        responses = await asyncio.gather(*tasks)

        # Process responses
        for response in responses:
            # Assertions
            assert response is not None
            assert hasattr(response, "category")
            assert response.category in categories
            assert hasattr(response, "reasoning")
            assert isinstance(response.reasoning, str)
            assert len(response.reasoning) > 0

    def test_granular_classification(self, vector_db_setup, categories):
        """Test the classification using finer-grained tasks directly."""
        # Example text to classify
        text = "I need to file a claim for water damage in my basement."
        logger.info(f"Classifying using finer-grained tasks: {text}")

        # Step 1: Embed the text
        embedding_request = EmbeddingTaskRequest(
            texts=[text],
            model=get_config_value("embedding_model", "BAAI/bge-reranker-large"),
        )
        embedding_response = embed_texts(embedding_request)
        assert (
            embedding_response.success
        ), f"Failed to generate embeddings: {embedding_response.error}"

        # Step 2: Search for similar examples
        vdb_search_request = VDBSearchRequest(
            query_texts=[text],
            query_embeddings=embedding_response.embeddings,
            collection_name=get_config_value("collection_name", "simple"),
            k=get_config_value("num_examples", 5),
        )
        vdb_search_response = search_vdb(vdb_search_request)
        assert (
            vdb_search_response.success
        ), f"Failed to search vector database: {vdb_search_response.error}"
        assert (
            vdb_search_response.results and vdb_search_response.results[0]
        ), "No similar examples found"

        examples = vdb_search_response.results[0]

        # Step 3 & 4: Classify using LLM (prompt creation is now handled inside the LLM task)
        llm_request = ClassificationLLMRequest(
            text=text,
            categories=categories,
            examples=examples,
            model=get_config_value("llm_model", "Qwen/Qwen2.5-1.5B-Instruct"),
            max_tokens=get_config_value("max_tokens", 4096),
            temperature=get_config_value("temperature", 0.0),
        )
        llm_response = classify_with_llm(llm_request)
        assert (
            llm_response.success
        ), f"Failed to classify with LLM: {llm_response.error}"

        # Log the raw LLM response for debugging
        logger.info(f"Raw LLM response: {llm_response.response_text}")

        # Step 5: Parse the response
        parsed_response = extract_classification_response(llm_response.response_text)
        logger.info(f"Parsed response: {parsed_response}")

        # Basic validation of the parsed response
        assert parsed_response is not None, "Parsed response should not be None"
        assert (
            "category" in parsed_response
        ), "Parsed response should have 'category' key"

        # If category is Unknown, log the reason but don't fail the test
        if parsed_response["category"] == "Unknown":
            logger.warning(
                f"Classification failed: {parsed_response.get('reasoning', 'No reasoning provided')}"
            )
        else:
            assert (
                parsed_response["category"] in categories
            ), f"Category '{parsed_response['category']}' not in expected categories"
            assert (
                "reasoning" in parsed_response
            ), "Parsed response should have 'reasoning' key"
            assert isinstance(
                parsed_response["reasoning"], str
            ), "Reasoning should be a string"
            assert (
                len(parsed_response["reasoning"]) > 0
            ), "Reasoning should not be empty"
