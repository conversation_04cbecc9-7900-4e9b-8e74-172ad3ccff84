"""
Base test classes and utilities for the legoAgent testing framework.

This module provides base classes and utilities that are shared across
different types of tests (unit, integration, functional, performance).
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union
from unittest.mock import MagicMock, AsyncMock

import pytest
from pydantic import BaseModel

from lego_agent.core.di import dependency_registry, DependencyRegistry
from lego_agent.core.service.base import BaseService, BaseServiceInstance
from lego_agent.core.service.llm import LLMService
from lego_agent.core.service.vdb import VectorDBService
from lego_agent.core.service.embedding import EmbeddingService
from lego_agent.core.services_manager import BaseServiceManager


class BaseTestCase(ABC):
    """Base class for all test cases in legoAgent.

    Provides common setup, teardown, and utility methods that can be
    used across different test types.
    """

    def setup_method(self):
        """Setup method called before each test method."""
        self.start_time = time.time()
        self._setup_logging()
        self._setup_dependency_registry()

    def teardown_method(self):
        """Teardown method called after each test method."""
        self.execution_time = time.time() - self.start_time
        self._cleanup_dependency_registry()

    def _setup_logging(self):
        """Setup test-specific logging configuration."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.setLevel(logging.DEBUG)

    def _setup_dependency_registry(self):
        """Setup a fresh dependency registry for each test."""
        global dependency_registry
        dependency_registry = DependencyRegistry()

    def _cleanup_dependency_registry(self):
        """Clean up the dependency registry after each test."""
        # Registry will be replaced in next test setup
        pass

    def assert_execution_time_under(self, max_seconds: float):
        """Assert that test execution time is under the specified limit."""
        assert hasattr(self, "execution_time"), "Test execution time not tracked"
        assert (
            self.execution_time < max_seconds
        ), f"Test took {self.execution_time:.3f}s, expected under {max_seconds}s"


class UnitTestCase(BaseTestCase):
    """Base class for unit tests.

    Unit tests focus on testing individual components in isolation,
    with all dependencies mocked.
    """

    def setup_method(self):
        """Setup for unit tests with comprehensive mocking."""
        super().setup_method()
        self.mocks = {}
        self._setup_service_mocks()

    def _setup_service_mocks(self):
        """Setup mock services for unit testing."""
        # This will be populated by specific test classes
        pass

    def create_mock_service(
        self, service_class: Type[BaseService], **kwargs
    ) -> MagicMock:
        """Create a mock service instance."""
        mock = MagicMock(spec=service_class)
        mock.service_name = kwargs.get("service_name", service_class.__name__.lower())
        return mock


class IntegrationTestCase(BaseTestCase):
    """Base class for integration tests.

    Integration tests focus on testing interactions between components,
    with some real implementations and some mocks.
    """

    def setup_method(self):
        """Setup for integration tests with selective mocking."""
        super().setup_method()
        self.real_services = {}
        self.mock_services = {}
        self._setup_test_services()

    def _setup_test_services(self):
        """Setup a mix of real and mock services for integration testing."""
        # This will be implemented by specific test classes
        pass


class FunctionalTestCase(BaseTestCase):
    """Base class for functional/end-to-end tests.

    Functional tests focus on testing complete workflows and user scenarios,
    typically with minimal mocking.
    """

    def setup_method(self):
        """Setup for functional tests with minimal mocking."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup a realistic test environment for functional testing."""
        # This will be implemented by specific test classes
        pass


class PerformanceTestCase(BaseTestCase):
    """Base class for performance tests.

    Performance tests focus on benchmarking and measuring the performance
    characteristics of the system.
    """

    def setup_method(self):
        """Setup for performance tests with timing and metrics."""
        super().setup_method()
        self.metrics = {}
        self._setup_performance_monitoring()

    def _setup_performance_monitoring(self):
        """Setup performance monitoring and metrics collection."""
        self.metrics["start_time"] = time.time()
        self.metrics["memory_usage"] = {}
        self.metrics["operation_times"] = []

    def record_operation_time(self, operation_name: str, duration: float):
        """Record the time taken for a specific operation."""
        self.metrics["operation_times"].append(
            {
                "operation": operation_name,
                "duration": duration,
                "timestamp": time.time(),
            }
        )

    def assert_operation_under_threshold(
        self, operation_name: str, max_duration: float
    ):
        """Assert that a specific operation completed under the time threshold."""
        operation_times = [
            op
            for op in self.metrics["operation_times"]
            if op["operation"] == operation_name
        ]
        assert operation_times, f"No recorded times for operation: {operation_name}"

        latest_time = operation_times[-1]["duration"]
        assert latest_time < max_duration, (
            f"Operation '{operation_name}' took {latest_time:.3f}s, "
            f"expected under {max_duration}s"
        )


class AsyncTestCase(BaseTestCase):
    """Base class for async tests.

    Provides utilities for testing async operations and workflows.
    """

    def setup_method(self):
        """Setup for async tests."""
        super().setup_method()
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def teardown_method(self):
        """Cleanup async resources."""
        super().teardown_method()
        if self.loop and not self.loop.is_closed():
            self.loop.close()

    async def run_async_test(self, coro):
        """Run an async test with proper error handling."""
        try:
            return await coro
        except Exception as e:
            self.logger.error(f"Async test failed: {e}")
            raise

    def create_async_mock(self, return_value=None, side_effect=None) -> AsyncMock:
        """Create an async mock with optional return value or side effect."""
        mock = AsyncMock()
        if return_value is not None:
            mock.return_value = return_value
        if side_effect is not None:
            mock.side_effect = side_effect
        return mock


# Test utilities and helper functions


def create_test_request(request_class: Type[BaseModel], **kwargs) -> BaseModel:
    """Create a test request object with default values."""
    # Get default values for the request class
    defaults = {}
    for field_name, field_info in request_class.__fields__.items():
        if field_info.default is not None:
            defaults[field_name] = field_info.default
        elif field_info.default_factory is not None:
            defaults[field_name] = field_info.default_factory()

    # Override with provided kwargs
    defaults.update(kwargs)
    return request_class(**defaults)


def assert_response_structure(response: BaseModel, expected_fields: List[str]):
    """Assert that a response has the expected field structure."""
    response_dict = response.dict()
    for field in expected_fields:
        assert field in response_dict, f"Expected field '{field}' not found in response"


def assert_async_response_time(max_seconds: float):
    """Decorator to assert async function execution time."""

    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            assert execution_time < max_seconds, (
                f"Async operation took {execution_time:.3f}s, "
                f"expected under {max_seconds}s"
            )
            return result

        return wrapper

    return decorator
