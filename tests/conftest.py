"""
Global test configuration for all tests.

This module provides the main pytest configuration and global fixtures
for the legoAgent testing framework.
"""

import asyncio
import logging
import os
import sys
import time
from pathlib import Path

import pytest

# Add the src directory to the Python path for test imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../src")))

from lego_agent.core.di import dependency_registry, DependencyRegistry
from lego_agent.core.service.llm import LLMService
from lego_agent.core.service.embedding import EmbeddingService
from lego_agent.core.service.vdb import VectorDBService
from lego_agent.core.services_manager import BaseServiceManager
from lego_agent.core.config import config_manager

# Import all fixtures to make them available
from tests.fixtures.common import *
from tests.fixtures.services import *
from tests.fixtures.workflows import *


# Configure pytest-asyncio
pytest_plugins = ("pytest_asyncio",)


def pytest_configure(config):
    """Configure pytest with custom markers and settings."""
    # Register custom markers
    markers = [
        "unit: Unit tests for individual components",
        "integration: Integration tests for component interactions",
        "functional: End-to-end functional tests",
        "performance: Performance and benchmark tests",
        "slow: Tests that take a long time to run",
        "async_test: Async tests requiring special handling",
    ]

    for marker in markers:
        config.addinivalue_line("markers", marker)


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on test location."""
    for item in items:
        # Add markers based on test file location
        test_path = Path(item.fspath)

        if "unit" in test_path.parts:
            item.add_marker(pytest.mark.unit)
        elif "integration" in test_path.parts:
            item.add_marker(pytest.mark.integration)
        elif "functional" in test_path.parts:
            item.add_marker(pytest.mark.functional)
        elif "performance" in test_path.parts:
            item.add_marker(pytest.mark.performance)

        # Mark async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.async_test)

        # Mark slow tests based on naming convention
        if "slow" in item.name or "performance" in item.name:
            item.add_marker(pytest.mark.slow)


@pytest.fixture(autouse=True)
def setup_test_env():
    """Setup global test environment for each test."""
    # Create necessary directories
    os.makedirs(".cache/test-reports", exist_ok=True)
    os.makedirs(".cache/htmlcov", exist_ok=True)
    os.makedirs(".cache/performance", exist_ok=True)

    # Setup test logging
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(".cache/test-reports/test.log"),
        ],
    )

    # Record test start time
    start_time = time.time()

    yield

    # Record test execution time
    execution_time = time.time() - start_time

    # Log slow tests
    if execution_time > 1.0:
        logging.warning(f"Slow test detected: {execution_time:.3f}s")


@pytest.fixture(autouse=True)
def reset_dependency_registry():
    """Reset the dependency registry before each test."""
    global dependency_registry
    # Create a fresh registry for each test to avoid test interference
    dependency_registry = DependencyRegistry()

    yield

    # Registry will be replaced in next test setup


@pytest.fixture(autouse=True)
def reset_config_manager():
    """Reset the configuration manager before each test."""
    # Store original configs
    original_configs = config_manager.configs.copy()

    yield

    # Restore original configs
    config_manager.configs = original_configs


@pytest.fixture
def test_logger():
    """Provide a test-specific logger."""
    logger = logging.getLogger("test")
    logger.setLevel(logging.DEBUG)
    return logger


@pytest.fixture
def temp_test_dir(tmp_path):
    """Provide a temporary directory for test files."""
    test_dir = tmp_path / "test_data"
    test_dir.mkdir()
    return test_dir


@pytest.fixture
def performance_tracker():
    """Provide a performance tracking utility for tests."""

    class PerformanceTracker:
        def __init__(self):
            self.metrics = {}
            self.start_times = {}

        def start_timer(self, operation: str):
            self.start_times[operation] = time.time()

        def end_timer(self, operation: str):
            if operation in self.start_times:
                duration = time.time() - self.start_times[operation]
                self.metrics[operation] = duration
                del self.start_times[operation]
                return duration
            return None

        def get_metric(self, operation: str):
            return self.metrics.get(operation)

        def assert_under_threshold(self, operation: str, threshold: float):
            duration = self.metrics.get(operation)
            assert duration is not None, f"No metric recorded for {operation}"
            assert (
                duration < threshold
            ), f"{operation} took {duration:.3f}s, expected under {threshold}s"

    return PerformanceTracker()


# Legacy fixtures for backward compatibility
@pytest.fixture
def mock_llm_service():
    """Create a basic mock LLM service for backward compatibility."""
    from tests.mocks.services import MockLLMService

    service = MockLLMService(responses=["Mock response"], delay=0.0)
    dependency_registry.register(LLMService, service)
    return service


@pytest.fixture
def mock_vdb_service():
    """Create a basic mock VectorDB service for backward compatibility."""
    from tests.mocks.services import MockVectorDBService

    service = MockVectorDBService(delay=0.0)
    dependency_registry.register(VectorDBService, service)
    return service
