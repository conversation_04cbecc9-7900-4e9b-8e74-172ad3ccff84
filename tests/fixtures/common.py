"""
Common test fixtures and data used across multiple test modules.
"""

import pytest
from typing import Dict, List, Any


@pytest.fixture
def sample_text_data() -> List[str]:
    """Sample text data for testing."""
    return [
        "This is a sample text for testing purposes.",
        "Another example text with different content.",
        "A third piece of text to ensure variety in testing.",
        "Short text.",
        "A much longer piece of text that contains multiple sentences and provides more content for testing various text processing capabilities of the system.",
    ]


@pytest.fixture
def sample_embeddings() -> List[List[float]]:
    """Sample embedding vectors for testing."""
    return [
        [0.1, 0.2, 0.3, 0.4, 0.5] * 77,  # 385 dimensions (close to common 384)
        [0.2, 0.3, 0.4, 0.5, 0.6] * 77,
        [0.3, 0.4, 0.5, 0.6, 0.7] * 77,
        [0.4, 0.5, 0.6, 0.7, 0.8] * 77,
        [0.5, 0.6, 0.7, 0.8, 0.9] * 77,
    ]


@pytest.fixture
def sample_chat_messages() -> List[Dict[str, str]]:
    """Sample chat messages for testing LLM interactions."""
    return [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of France?"},
        {"role": "assistant", "content": "The capital of France is Paris."},
        {"role": "user", "content": "What about Germany?"},
        {"role": "assistant", "content": "The capital of Germany is Berlin."},
    ]


@pytest.fixture
def test_config_data() -> Dict[str, Any]:
    """Test configuration data."""
    return {
        "anthropic.chain": {
            "model": "test-model",
            "max_tokens": 1000,
        },
        "anthropic.parallel": {
            "model": "test-model",
            "n_workers": 3,
            "max_tokens": 500,
        },
        "anthropic.route": {
            "model": "test-model",
            "show_reasoning": True,
        },
        "anthropic.loop": {
            "model": "test-model",
            "max_iterations": 5,
            "show_thoughts": False,
        },
        "anthropic.planner_workers": {
            "model": "test-model",
            "max_subtasks": 5,
        },
        "core.service.llm": {
            "default_model": "test-model",
            "instances": [
                {
                    "name": "test-instance",
                    "location": "127.0.0.1:8001",
                    "model": "test-model",
                }
            ],
        },
        "core.service.embedding": {
            "default_model": "test-embedding-model",
            "instances": [
                {
                    "name": "test-embedding-instance",
                    "location": "127.0.0.1:8002",
                    "model": "test-embedding-model",
                }
            ],
        },
        "core.service.vdb": {
            "default_collection": "test_collection",
            "instances": [
                {
                    "name": "test-vdb-instance",
                    "location": "127.0.0.1:19530",
                    "type": "milvus",
                }
            ],
        },
    }


@pytest.fixture
def sample_documents() -> List[Dict[str, Any]]:
    """Sample documents for vector database testing."""
    return [
        {
            "id": "doc_1",
            "text": "Machine learning is a subset of artificial intelligence.",
            "metadata": {
                "category": "AI",
                "author": "John Doe",
                "timestamp": "2024-01-01T00:00:00Z",
            },
        },
        {
            "id": "doc_2",
            "text": "Natural language processing enables computers to understand human language.",
            "metadata": {
                "category": "NLP",
                "author": "Jane Smith",
                "timestamp": "2024-01-02T00:00:00Z",
            },
        },
        {
            "id": "doc_3",
            "text": "Deep learning uses neural networks with multiple layers.",
            "metadata": {
                "category": "Deep Learning",
                "author": "Bob Johnson",
                "timestamp": "2024-01-03T00:00:00Z",
            },
        },
        {
            "id": "doc_4",
            "text": "Computer vision allows machines to interpret visual information.",
            "metadata": {
                "category": "Computer Vision",
                "author": "Alice Brown",
                "timestamp": "2024-01-04T00:00:00Z",
            },
        },
        {
            "id": "doc_5",
            "text": "Reinforcement learning trains agents through rewards and penalties.",
            "metadata": {
                "category": "RL",
                "author": "Charlie Wilson",
                "timestamp": "2024-01-05T00:00:00Z",
            },
        },
    ]


@pytest.fixture
def sample_classification_categories() -> List[str]:
    """Sample categories for classification testing."""
    return [
        "Technology",
        "Science",
        "Business",
        "Health",
        "Education",
        "Entertainment",
        "Sports",
        "Politics",
        "Environment",
        "Travel",
    ]


@pytest.fixture
def sample_prompts() -> Dict[str, str]:
    """Sample prompts for various testing scenarios."""
    return {
        "summarization": "Please summarize the following text in 2-3 sentences:",
        "classification": "Classify the following text into one of these categories: {categories}",
        "question_answering": "Answer the following question based on the provided context:",
        "translation": "Translate the following text to French:",
        "sentiment_analysis": "Analyze the sentiment of the following text (positive, negative, or neutral):",
        "code_generation": "Generate Python code for the following task:",
        "creative_writing": "Write a creative story based on the following prompt:",
        "analysis": "Provide a detailed analysis of the following topic:",
    }


@pytest.fixture
def performance_thresholds() -> Dict[str, float]:
    """Performance thresholds for testing."""
    return {
        "llm_response_time": 5.0,  # seconds
        "embedding_response_time": 2.0,  # seconds
        "vdb_search_time": 1.0,  # seconds
        "workflow_chain_time": 10.0,  # seconds
        "workflow_parallel_time": 8.0,  # seconds
        "workflow_route_time": 6.0,  # seconds
        "memory_usage_mb": 500,  # MB
        "concurrent_requests": 10,  # number of concurrent requests
    }


@pytest.fixture
def error_scenarios() -> Dict[str, Dict[str, Any]]:
    """Common error scenarios for testing."""
    return {
        "network_timeout": {
            "exception": "TimeoutError",
            "message": "Request timed out",
            "retry_count": 3,
        },
        "service_unavailable": {
            "exception": "ConnectionError",
            "message": "Service unavailable",
            "retry_count": 2,
        },
        "invalid_input": {
            "exception": "ValueError",
            "message": "Invalid input format",
            "retry_count": 0,
        },
        "rate_limit": {
            "exception": "RateLimitError",
            "message": "Rate limit exceeded",
            "retry_count": 5,
        },
        "authentication_failed": {
            "exception": "AuthenticationError",
            "message": "Authentication failed",
            "retry_count": 0,
        },
    }
