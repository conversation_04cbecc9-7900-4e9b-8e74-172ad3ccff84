"""
Service-related test fixtures.
"""

import pytest
from unittest.mock import MagicMock

from lego_agent.core.di import dependency_registry
from lego_agent.core.service.llm import LLMService
from lego_agent.core.service.embedding import EmbeddingService
from lego_agent.core.service.vdb import VectorDBService
from lego_agent.core.services_manager import BaseServiceManager
from lego_agent.core.config import config_manager

from tests.mocks.services import (
    MockLLMService,
    MockEmbeddingService,
    MockVectorDBService,
    MockServiceManager,
)


@pytest.fixture
def mock_llm_service():
    """Create a mock LLM service with predefined responses."""
    responses = [
        "This is a mock response from the LLM service.",
        "Another mock response for testing purposes.",
        "A third response to test multiple interactions.",
    ]
    service = MockLLMService(responses=responses, delay=0.1)
    dependency_registry.register(LLMService, service)
    return service


@pytest.fixture
def mock_embedding_service():
    """Create a mock embedding service."""
    service = MockEmbeddingService(embedding_dim=384, delay=0.05)
    dependency_registry.register(EmbeddingService, service)
    return service


@pytest.fixture
def mock_vdb_service():
    """Create a mock vector database service."""
    service = MockVectorDBService(delay=0.02)
    dependency_registry.register(VectorDBService, service)
    return service


@pytest.fixture
def mock_service_manager(mock_llm_service, mock_embedding_service, mock_vdb_service):
    """Create a mock service manager with all services."""
    manager = MockServiceManager()
    manager.add_mock_service("llm", mock_llm_service)
    manager.add_mock_service("embedding", mock_embedding_service)
    manager.add_mock_service("vdb", mock_vdb_service)
    dependency_registry.register(BaseServiceManager, manager)
    return manager


@pytest.fixture
def configured_service_manager(test_config_data):
    """Create a service manager with test configuration."""
    # Set up test configuration
    for config_key, config_value in test_config_data.items():
        config_manager.configs[config_key] = config_value

    # Create service manager with mock services
    manager = MockServiceManager()

    # Add configured mock services
    llm_service = MockLLMService(responses=["Configured mock LLM response"], delay=0.0)
    embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.0)
    vdb_service = MockVectorDBService(delay=0.0)

    manager.add_mock_service("llm", llm_service)
    manager.add_mock_service("embedding", embedding_service)
    manager.add_mock_service("vdb", vdb_service)

    dependency_registry.register(BaseServiceManager, manager)

    yield manager

    # Cleanup configuration
    for config_key in test_config_data.keys():
        if config_key in config_manager.configs:
            del config_manager.configs[config_key]


@pytest.fixture
def llm_service_with_errors():
    """Create a mock LLM service that simulates various error conditions."""
    service = MockLLMService(responses=["Error response"], delay=0.0)

    # Add error simulation methods
    def simulate_timeout():
        import time

        time.sleep(10)  # Simulate timeout
        return "Timeout response"

    def simulate_rate_limit():
        raise Exception("Rate limit exceeded")

    def simulate_network_error():
        raise ConnectionError("Network error")

    service.simulate_timeout = simulate_timeout
    service.simulate_rate_limit = simulate_rate_limit
    service.simulate_network_error = simulate_network_error

    return service


@pytest.fixture
def embedding_service_with_custom_dim():
    """Create a mock embedding service with custom dimensions."""

    def create_service(embedding_dim: int = 768):
        service = MockEmbeddingService(embedding_dim=embedding_dim, delay=0.0)
        dependency_registry.register(EmbeddingService, service)
        return service

    return create_service


@pytest.fixture
def vdb_service_with_data(sample_documents):
    """Create a mock vector DB service pre-populated with test data."""
    service = MockVectorDBService(delay=0.0)

    # Pre-populate with sample documents
    for doc in sample_documents:
        service.stored_vectors.setdefault("test_collection", []).append(
            {
                "id": doc["id"],
                "vector": [0.1] * 384,  # Mock embedding
                "metadata": doc["metadata"],
            }
        )

    dependency_registry.register(VectorDBService, service)
    return service


@pytest.fixture
def service_manager_with_health_checks():
    """Create a service manager with health check monitoring."""
    manager = MockServiceManager()

    # Add services with health checks
    llm_service = MockLLMService(responses=["Healthy LLM"], delay=0.0)
    embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.0)
    vdb_service = MockVectorDBService(delay=0.0)

    def llm_health_check(service):
        from lego_agent.core.services_manager import HealthCheckResult, ServiceStatus

        return HealthCheckResult(ServiceStatus.HEALTHY, "LLM service is healthy")

    def embedding_health_check(service):
        from lego_agent.core.services_manager import HealthCheckResult, ServiceStatus

        return HealthCheckResult(ServiceStatus.HEALTHY, "Embedding service is healthy")

    def vdb_health_check(service):
        from lego_agent.core.services_manager import HealthCheckResult, ServiceStatus

        return HealthCheckResult(ServiceStatus.HEALTHY, "VDB service is healthy")

    manager.add_service("llm", llm_service, llm_health_check)
    manager.add_service("embedding", embedding_service, embedding_health_check)
    manager.add_service("vdb", vdb_service, vdb_health_check)

    dependency_registry.register(BaseServiceManager, manager)
    return manager


@pytest.fixture
def async_service_manager():
    """Create a service manager optimized for async testing."""
    manager = MockServiceManager()

    # Create async-optimized mock services
    llm_service = MockLLMService(
        responses=["Async LLM response"],
        delay=0.01,  # Small delay to test async behavior
    )
    embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.01)
    vdb_service = MockVectorDBService(delay=0.01)

    manager.add_mock_service("llm", llm_service)
    manager.add_mock_service("embedding", embedding_service)
    manager.add_mock_service("vdb", vdb_service)

    dependency_registry.register(BaseServiceManager, manager)
    return manager


@pytest.fixture
def performance_test_services():
    """Create services configured for performance testing."""
    manager = MockServiceManager()

    # Create services with performance tracking
    llm_service = MockLLMService(
        responses=["Performance test response"] * 100,
        delay=0.001,  # Minimal delay for performance testing
    )
    embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.001)
    vdb_service = MockVectorDBService(delay=0.001)

    # Add performance tracking
    for service in [llm_service, embedding_service, vdb_service]:
        service.performance_metrics = {
            "total_requests": 0,
            "total_time": 0.0,
            "average_time": 0.0,
            "min_time": float("inf"),
            "max_time": 0.0,
        }

        original_serve = service.serve
        original_serve_async = service.serve_async

        def track_performance(original_method):
            def wrapper(*args, **kwargs):
                import time

                start_time = time.time()
                result = original_method(*args, **kwargs)
                end_time = time.time()

                duration = end_time - start_time
                metrics = service.performance_metrics
                metrics["total_requests"] += 1
                metrics["total_time"] += duration
                metrics["average_time"] = (
                    metrics["total_time"] / metrics["total_requests"]
                )
                metrics["min_time"] = min(metrics["min_time"], duration)
                metrics["max_time"] = max(metrics["max_time"], duration)

                return result

            return wrapper

        async def track_performance_async(original_method):
            async def wrapper(*args, **kwargs):
                import time

                start_time = time.time()
                result = await original_method(*args, **kwargs)
                end_time = time.time()

                duration = end_time - start_time
                metrics = service.performance_metrics
                metrics["total_requests"] += 1
                metrics["total_time"] += duration
                metrics["average_time"] = (
                    metrics["total_time"] / metrics["total_requests"]
                )
                metrics["min_time"] = min(metrics["min_time"], duration)
                metrics["max_time"] = max(metrics["max_time"], duration)

                return result

            return wrapper

        service.serve = track_performance(original_serve)
        service.serve_async = track_performance_async(original_serve_async)

    manager.add_mock_service("llm", llm_service)
    manager.add_mock_service("embedding", embedding_service)
    manager.add_mock_service("vdb", vdb_service)

    dependency_registry.register(BaseServiceManager, manager)
    return manager
