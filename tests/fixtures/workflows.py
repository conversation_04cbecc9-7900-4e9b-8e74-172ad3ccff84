"""
Workflow-related test fixtures.
"""

import pytest
from typing import Dict, List, Any

from lego_agent.agents.anthropic.patterns.workflow.chaining import ChainWorkflowRequest
from lego_agent.agents.anthropic.patterns.workflow.paralleling import (
    ParallelWorkflowRequest,
)
from lego_agent.agents.anthropic.patterns.workflow.routing import RouteWork<PERSON>Request
from lego_agent.agents.anthropic.patterns.workflow.looping import LoopWorkflowRequest
from lego_agent.agents.anthropic.patterns.workflow.planner_workers import (
    PlannerWorkersRequest,
)


@pytest.fixture
def chain_workflow_data() -> Dict[str, Any]:
    """Test data for chain workflow testing."""
    return {
        "simple_chain": ChainWorkflowRequest(
            input="Analyze the benefits of renewable energy",
            prompts=[
                "Identify the main types of renewable energy sources",
                "Explain the environmental benefits of each type",
                "Summarize the economic advantages",
            ],
            model="test-model",
        ),
        "complex_chain": ChainWorkflowRequest(
            input="Design a machine learning system for image classification",
            prompts=[
                "Define the problem and requirements",
                "Choose appropriate algorithms and architectures",
                "Design the data preprocessing pipeline",
                "Plan the training and evaluation strategy",
                "Outline deployment considerations",
            ],
            model="test-model",
        ),
        "short_chain": ChainWorkflowRequest(
            input="What is Python?",
            prompts=["Provide a brief definition", "Give a simple example"],
            model="test-model",
        ),
    }


@pytest.fixture
def parallel_workflow_data() -> Dict[str, Any]:
    """Test data for parallel workflow testing."""
    return {
        "sentiment_analysis": ParallelWorkflowRequest(
            prompt="Analyze the sentiment of this text (positive, negative, or neutral):",
            inputs=[
                "I love this product! It's amazing!",
                "This is terrible, I hate it.",
                "It's okay, nothing special.",
                "Absolutely fantastic experience!",
                "Could be better, but not bad.",
            ],
            n_workers=3,
            model="test-model",
        ),
        "translation": ParallelWorkflowRequest(
            prompt="Translate this text to French:",
            inputs=[
                "Hello, how are you?",
                "Good morning!",
                "Thank you very much.",
                "See you later.",
                "Have a nice day!",
            ],
            n_workers=2,
            model="test-model",
        ),
        "summarization": ParallelWorkflowRequest(
            prompt="Summarize this text in one sentence:",
            inputs=[
                "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.",
                "Natural language processing is a branch of AI that helps computers understand, interpret and manipulate human language.",
                "Computer vision is a field of AI that trains computers to interpret and understand the visual world.",
            ],
            n_workers=2,
            model="test-model",
        ),
    }


@pytest.fixture
def route_workflow_data() -> Dict[str, Any]:
    """Test data for route workflow testing."""
    return {
        "customer_support": RouteWorkflowRequest(
            input="I can't log into my account and need help resetting my password",
            routes={
                "technical": "Provide technical troubleshooting steps for login issues",
                "account": "Help with account management and password reset procedures",
                "billing": "Assist with billing and payment related questions",
                "general": "Provide general customer support assistance",
            },
            model="test-model",
            show_reasoning=True,
        ),
        "content_classification": RouteWorkflowRequest(
            input="The latest research in quantum computing shows promising results for cryptography applications",
            routes={
                "technology": "Analyze from a technology perspective",
                "science": "Provide scientific analysis and implications",
                "business": "Discuss business and market implications",
                "education": "Explain in educational terms",
            },
            model="test-model",
            show_reasoning=False,
        ),
        "simple_routing": RouteWorkflowRequest(
            input="What's the weather like today?",
            routes={
                "weather": "Provide weather information and forecasts",
                "general": "Handle general questions and inquiries",
            },
            model="test-model",
        ),
    }


@pytest.fixture
def loop_workflow_data() -> Dict[str, Any]:
    """Test data for loop workflow testing."""
    return {
        "code_generation": LoopWorkflowRequest(
            task="Write a Python function that calculates the factorial of a number",
            max_iterations=3,
            show_thoughts=True,
            return_intermediate=True,
            model="test-model",
        ),
        "essay_writing": LoopWorkflowRequest(
            task="Write a 500-word essay about the importance of renewable energy",
            max_iterations=5,
            show_thoughts=False,
            return_intermediate=False,
            model="test-model",
        ),
        "problem_solving": LoopWorkflowRequest(
            task="Solve this math problem: If a train travels 120 km in 2 hours, what is its average speed?",
            max_iterations=2,
            show_thoughts=True,
            return_intermediate=True,
            model="test-model",
        ),
    }


@pytest.fixture
def planner_workers_data() -> Dict[str, Any]:
    """Test data for planner-workers workflow testing."""
    return {
        "research_project": PlannerWorkersRequest(
            task="Research the impact of artificial intelligence on healthcare",
            max_subtasks=4,
            model="test-model",
        ),
        "product_development": PlannerWorkersRequest(
            task="Develop a mobile app for fitness tracking",
            max_subtasks=5,
            model="test-model",
        ),
        "marketing_campaign": PlannerWorkersRequest(
            task="Create a marketing campaign for a new eco-friendly product",
            max_subtasks=3,
            model="test-model",
        ),
        "simple_task": PlannerWorkersRequest(
            task="Plan a birthday party", max_subtasks=3, model="test-model"
        ),
    }


@pytest.fixture
def workflow_performance_data() -> Dict[str, Any]:
    """Performance testing data for workflows."""
    return {
        "large_parallel": ParallelWorkflowRequest(
            prompt="Classify this text:",
            inputs=[f"Sample text number {i}" for i in range(20)],
            n_workers=5,
            model="test-model",
        ),
        "long_chain": ChainWorkflowRequest(
            input="Complex analysis task",
            prompts=[f"Step {i}: Process the input" for i in range(10)],
            model="test-model",
        ),
        "complex_routing": RouteWorkflowRequest(
            input="Multi-domain query requiring complex routing",
            routes={f"route_{i}": f"Handle route {i}" for i in range(10)},
            model="test-model",
        ),
    }


@pytest.fixture
def workflow_error_scenarios() -> Dict[str, Any]:
    """Error scenarios for workflow testing."""
    return {
        "empty_input": {
            "chain": ChainWorkflowRequest(
                input="", prompts=["Process this input"], model="test-model"
            ),
            "parallel": ParallelWorkflowRequest(
                prompt="Process this:", inputs=[], model="test-model"
            ),
        },
        "invalid_model": {
            "chain": ChainWorkflowRequest(
                input="Test input", prompts=["Process this"], model="invalid-model"
            )
        },
        "missing_routes": {
            "route": RouteWorkflowRequest(
                input="Test input", routes={}, model="test-model"
            )
        },
        "zero_iterations": {
            "loop": LoopWorkflowRequest(
                task="Test task", max_iterations=0, model="test-model"
            )
        },
    }


@pytest.fixture
def workflow_async_data() -> Dict[str, Any]:
    """Data specifically for async workflow testing."""
    return {
        "concurrent_chains": [
            ChainWorkflowRequest(
                input=f"Input {i}",
                prompts=[f"Step 1 for input {i}", f"Step 2 for input {i}"],
                model="test-model",
            )
            for i in range(5)
        ],
        "concurrent_parallel": [
            ParallelWorkflowRequest(
                prompt=f"Process batch {i}:",
                inputs=[f"Item {i}_{j}" for j in range(3)],
                n_workers=2,
                model="test-model",
            )
            for i in range(3)
        ],
        "mixed_workflows": {
            "chain": ChainWorkflowRequest(
                input="Chain input",
                prompts=["Chain step 1", "Chain step 2"],
                model="test-model",
            ),
            "parallel": ParallelWorkflowRequest(
                prompt="Parallel process:",
                inputs=["Parallel input 1", "Parallel input 2"],
                n_workers=2,
                model="test-model",
            ),
            "route": RouteWorkflowRequest(
                input="Route input",
                routes={"route1": "Handle route 1", "route2": "Handle route 2"},
                model="test-model",
            ),
        },
    }
