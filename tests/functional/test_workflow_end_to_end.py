"""
End-to-end functional tests for legoAgent workflows.

This module tests complete workflow scenarios from start to finish.
"""

import asyncio
import pytest

from tests.base import FunctionalTestCase
from tests.mocks.services import MockServiceManager, MockLLMService

from lego_agent.core.di import dependency_registry
from lego_agent.core.services_manager import BaseServiceManager
from lego_agent.agents.anthropic.patterns.workflow.chaining import (
    chain,
    chain_async,
    ChainWorkflowRequest,
)
from lego_agent.agents.anthropic.patterns.workflow.paralleling import (
    parallel,
    parallel_async,
    ParallelWorkflowRequest,
)
from lego_agent.agents.anthropic.patterns.workflow.routing import (
    route,
    route_async,
    RouteWorkflowRequest,
)


@pytest.mark.functional
class TestChainWorkflowEndToEnd(FunctionalTestCase):
    """End-to-end tests for chain workflow."""

    def setup_method(self):
        """Setup for chain workflow tests."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup realistic test environment for chain workflow."""
        # Create LLM service with realistic responses
        self.llm_service = MockLLMService(
            responses=[
                "Step 1: Renewable energy includes solar, wind, and hydroelectric power.",
                "Step 2: These sources reduce carbon emissions and environmental impact.",
                "Step 3: Economic benefits include job creation and energy independence.",
            ],
            delay=0.05,  # Simulate realistic response time
        )

        # Setup service manager
        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_chain_workflow_complete_scenario(self, chain_workflow_data):
        """Test complete chain workflow scenario."""
        request = chain_workflow_data["simple_chain"]

        # Execute the workflow
        response = chain(request)

        # Verify workflow completion
        assert response is not None
        assert response.output is not None
        assert len(response.steps) == len(request.prompts)

        # Verify service interactions
        assert self.llm_service.call_count == len(request.prompts)

        # Verify step progression
        for i, step in enumerate(response.steps):
            assert step is not None
            assert len(step) > 0

    @pytest.mark.asyncio
    async def test_chain_workflow_async_complete(self, chain_workflow_data):
        """Test complete async chain workflow."""
        request = chain_workflow_data["complex_chain"]

        # Execute async workflow
        response = await chain_async(request)

        # Verify async completion
        assert response is not None
        assert response.output is not None
        assert len(response.steps) == len(request.prompts)
        assert self.llm_service.call_count == len(request.prompts)

    def test_chain_workflow_error_handling(self):
        """Test chain workflow error handling."""
        # Create a service that will fail
        failing_service = MockLLMService(responses=[], delay=0.0)

        def failing_serve(request):
            raise Exception("Service failure")

        failing_service.serve = failing_serve

        self.service_manager.add_mock_service("llm", failing_service)

        request = ChainWorkflowRequest(
            input="Test input", prompts=["Test prompt"], model="test-model"
        )

        # Verify error handling
        with pytest.raises(Exception):
            chain(request)

    def test_chain_workflow_performance(self, performance_tracker, chain_workflow_data):
        """Test chain workflow performance."""
        request = chain_workflow_data["short_chain"]

        performance_tracker.start_timer("chain_workflow")
        response = chain(request)
        performance_tracker.end_timer("chain_workflow")

        assert response is not None
        performance_tracker.assert_under_threshold("chain_workflow", 1.0)


@pytest.mark.functional
class TestParallelWorkflowEndToEnd(FunctionalTestCase):
    """End-to-end tests for parallel workflow."""

    def setup_method(self):
        """Setup for parallel workflow tests."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup test environment for parallel workflow."""
        # Create LLM service with sentiment analysis responses
        self.llm_service = MockLLMService(
            responses=[
                "Positive sentiment detected",
                "Negative sentiment detected",
                "Neutral sentiment detected",
                "Positive sentiment detected",
                "Neutral sentiment detected",
            ],
            delay=0.02,
        )

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_parallel_workflow_complete_scenario(self, parallel_workflow_data):
        """Test complete parallel workflow scenario."""
        request = parallel_workflow_data["sentiment_analysis"]

        # Execute parallel workflow
        response = parallel(request)

        # Verify parallel completion
        assert response is not None
        assert len(response.outputs) == len(request.inputs)

        # Verify all inputs were processed
        for output in response.outputs:
            assert output is not None
            assert "sentiment" in output.lower()

        # Verify service was called for each input
        assert self.llm_service.call_count == len(request.inputs)

    @pytest.mark.asyncio
    async def test_parallel_workflow_async_complete(self, parallel_workflow_data):
        """Test complete async parallel workflow."""
        request = parallel_workflow_data["translation"]

        # Execute async parallel workflow
        response = await parallel_async(request)

        # Verify async parallel completion
        assert response is not None
        assert len(response.outputs) == len(request.inputs)
        assert self.llm_service.call_count == len(request.inputs)

    def test_parallel_workflow_concurrency(
        self, parallel_workflow_data, performance_tracker
    ):
        """Test parallel workflow concurrency benefits."""
        request = parallel_workflow_data["summarization"]

        # Test with n_workers=1 (sequential)
        self.llm_service.reset()
        request.n_workers = 1

        performance_tracker.start_timer("sequential")
        response_sequential = parallel(request)
        performance_tracker.end_timer("sequential")

        # Test with n_workers=3 (parallel)
        self.llm_service.reset()
        request.n_workers = 3

        performance_tracker.start_timer("parallel")
        response_parallel = parallel(request)
        performance_tracker.end_timer("parallel")

        # Verify both produce same results
        assert len(response_sequential.outputs) == len(response_parallel.outputs)

        # Parallel should be faster (or at least not significantly slower)
        sequential_time = performance_tracker.get_metric("sequential")
        parallel_time = performance_tracker.get_metric("parallel")

        # Allow some tolerance for overhead
        assert parallel_time <= sequential_time * 1.5


@pytest.mark.functional
class TestRouteWorkflowEndToEnd(FunctionalTestCase):
    """End-to-end tests for route workflow."""

    def setup_method(self):
        """Setup for route workflow tests."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup test environment for route workflow."""
        # Create LLM service with routing responses
        self.llm_service = MockLLMService(
            responses=[
                # First response: routing decision
                """
                <reasoning>
                The user is asking about login issues and password reset, 
                which is clearly an account management issue.
                </reasoning>
                <selection>account</selection>
                """,
                # Second response: actual handling
                "Here are the steps to reset your password: 1) Go to login page, 2) Click 'Forgot Password', 3) Enter your email...",
            ],
            delay=0.03,
        )

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_route_workflow_complete_scenario(self, route_workflow_data):
        """Test complete route workflow scenario."""
        request = route_workflow_data["customer_support"]

        # Execute route workflow
        response = route(request)

        # Verify routing completion
        assert response is not None
        assert response.output is not None
        assert response.route_key is not None
        assert response.reasoning is not None

        # Verify correct route was selected
        assert response.route_key == "account"
        assert "password" in response.output.lower()

        # Verify two LLM calls (routing + processing)
        assert self.llm_service.call_count == 2

    @pytest.mark.asyncio
    async def test_route_workflow_async_complete(self, route_workflow_data):
        """Test complete async route workflow."""
        request = route_workflow_data["content_classification"]

        # Update responses for this scenario
        self.llm_service.responses = [
            """
            <reasoning>
            This content discusses quantum computing research and cryptography,
            which is primarily a technology topic.
            </reasoning>
            <selection>technology</selection>
            """,
            "From a technology perspective, quantum computing represents a paradigm shift...",
        ]
        self.llm_service.reset()

        # Execute async route workflow
        response = await route_async(request)

        # Verify async routing completion
        assert response is not None
        assert response.route_key == "technology"
        assert self.llm_service.call_count == 2

    def test_route_workflow_reasoning_display(self, route_workflow_data, capsys):
        """Test route workflow reasoning display."""
        request = route_workflow_data["customer_support"]
        request.show_reasoning = True

        # Execute workflow
        response = route(request)

        # Capture printed output
        captured = capsys.readouterr()

        # Verify reasoning was displayed
        assert "Routing Analysis:" in captured.out
        assert "account management" in captured.out.lower()
        assert "Selected route: account" in captured.out


@pytest.mark.functional
@pytest.mark.slow
class TestWorkflowIntegrationScenarios(FunctionalTestCase):
    """Integration scenarios combining multiple workflows."""

    def setup_method(self):
        """Setup for integration scenarios."""
        super().setup_method()
        self._setup_test_environment()

    def _setup_test_environment(self):
        """Setup comprehensive test environment."""
        self.llm_service = MockLLMService(
            responses=[
                "Analysis complete",
                "Processing step 1",
                "Processing step 2",
                "Final result",
            ]
            * 10,  # Enough responses for complex scenarios
            delay=0.01,
        )

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        dependency_registry.register(BaseServiceManager, self.service_manager)

    @pytest.mark.asyncio
    async def test_mixed_workflow_scenario(self):
        """Test scenario combining different workflow types."""
        # Step 1: Use routing to classify the task
        route_request = RouteWorkflowRequest(
            input="I need to analyze customer feedback and generate a report",
            routes={
                "analysis": "Perform data analysis",
                "reporting": "Generate reports",
                "customer_service": "Handle customer issues",
            },
            model="test-model",
        )

        # Mock routing response
        self.llm_service.responses = [
            "<reasoning>This requires analysis</reasoning><selection>analysis</selection>",
            "Analysis route selected",
        ]

        route_response = await route_async(route_request)
        assert route_response.route_key == "analysis"

        # Step 2: Use parallel processing for multiple data sources
        parallel_request = ParallelWorkflowRequest(
            prompt="Analyze this feedback data:",
            inputs=[
                "Customer feedback 1: Great product!",
                "Customer feedback 2: Could be improved",
                "Customer feedback 3: Excellent service",
            ],
            n_workers=2,
            model="test-model",
        )

        self.llm_service.reset()
        self.llm_service.responses = [
            "Positive analysis",
            "Negative analysis",
            "Positive analysis",
        ]

        parallel_response = await parallel_async(parallel_request)
        assert len(parallel_response.outputs) == 3

        # Step 3: Use chaining to create final report
        chain_request = ChainWorkflowRequest(
            input=f"Analysis results: {parallel_response.outputs}",
            prompts=[
                "Summarize the analysis results",
                "Generate executive summary",
                "Create action items",
            ],
            model="test-model",
        )

        self.llm_service.reset()
        self.llm_service.responses = [
            "Summary complete",
            "Executive summary ready",
            "Action items created",
        ]

        chain_response = await chain_async(chain_request)
        assert len(chain_response.steps) == 3

        # Verify the complete scenario
        assert route_response is not None
        assert parallel_response is not None
        assert chain_response is not None
