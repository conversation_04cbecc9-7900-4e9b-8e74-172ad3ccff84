"""
Integration tests for service manager and service interactions.

This module tests how services work together through the service manager.
"""

import asyncio
import pytest
from unittest.mock import patch

from tests.base import IntegrationTestCase
from tests.mocks.services import (
    MockServiceManager,
    MockLLMService,
    MockEmbeddingService,
    MockVectorDBService,
)
from tests.mocks.factories import (
    LLMServiceRequestFactory,
    EmbeddingServiceRequestFactory,
)

from lego_agent.core.di import dependency_registry
from lego_agent.core.services_manager import BaseServiceManager
from lego_agent.core.service.llm import LLMService, LLMServiceRequest
from lego_agent.core.service.embedding import EmbeddingService, EmbeddingServiceRequest
from lego_agent.core.service.vdb import VectorDBService, VectorDBServiceRequest


@pytest.mark.integration
class TestServiceManagerIntegration(IntegrationTestCase):
    """Integration tests for service manager."""

    def setup_method(self):
        """Setup for service manager integration tests."""
        super().setup_method()
        self._setup_test_services()

    def _setup_test_services(self):
        """Setup test services for integration testing."""
        # Create mock services
        self.llm_service = MockLLMService(
            responses=["Integration test response"], delay=0.01
        )
        self.embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.01)
        self.vdb_service = MockVectorDBService(delay=0.01)

        # Create service manager
        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        self.service_manager.add_mock_service("embedding", self.embedding_service)
        self.service_manager.add_mock_service("vdb", self.vdb_service)

        # Register in dependency registry
        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_service_manager_get_services(self):
        """Test getting services from service manager."""
        llm = self.service_manager.get_service("llm")
        embedding = self.service_manager.get_service("embedding")
        vdb = self.service_manager.get_service("vdb")

        assert llm is self.llm_service
        assert embedding is self.embedding_service
        assert vdb is self.vdb_service

    def test_service_manager_service_requests(self):
        """Test making requests through service manager."""
        # Test LLM request
        llm_request = LLMServiceRequestFactory()
        llm = self.service_manager.get_service("llm")
        llm_response = llm.serve(llm_request)

        assert llm_response is not None
        assert self.llm_service.call_count == 1

        # Test embedding request
        embedding_request = EmbeddingServiceRequestFactory()
        embedding = self.service_manager.get_service("embedding")
        embedding_response = embedding.serve(embedding_request)

        assert embedding_response is not None
        assert self.embedding_service.call_count == 1

    @pytest.mark.asyncio
    async def test_service_manager_async_requests(self):
        """Test async requests through service manager."""
        llm = self.service_manager.get_service("llm")
        embedding = self.service_manager.get_service("embedding")

        # Make concurrent async requests
        llm_request = LLMServiceRequestFactory()
        embedding_request = EmbeddingServiceRequestFactory()

        llm_task = llm.serve_async(llm_request)
        embedding_task = embedding.serve_async(embedding_request)

        llm_response, embedding_response = await asyncio.gather(
            llm_task, embedding_task
        )

        assert llm_response is not None
        assert embedding_response is not None
        assert self.llm_service.call_count == 1
        assert self.embedding_service.call_count == 1

    def test_service_manager_reset_all_mocks(self):
        """Test resetting all mock services."""
        # Make some requests
        llm = self.service_manager.get_service("llm")
        embedding = self.service_manager.get_service("embedding")

        llm.serve(LLMServiceRequestFactory())
        embedding.serve(EmbeddingServiceRequestFactory())

        assert self.llm_service.call_count == 1
        assert self.embedding_service.call_count == 1

        # Reset all mocks
        self.service_manager.reset_all_mocks()

        assert self.llm_service.call_count == 0
        assert self.embedding_service.call_count == 0

    def test_service_manager_health_checks(self):
        """Test service manager health check integration."""
        import time

        # Add health checks to services
        def llm_health_check(service):
            from lego_agent.core.services_manager import (
                HealthCheckResult,
                ServiceStatus,
            )

            return HealthCheckResult(ServiceStatus.HEALTHY, "LLM healthy")

        def embedding_health_check(service):
            from lego_agent.core.services_manager import (
                HealthCheckResult,
                ServiceStatus,
            )

            return HealthCheckResult(ServiceStatus.HEALTHY, "Embedding healthy")

        # Create new service manager with health checks
        health_manager = MockServiceManager()
        health_manager.add_service("llm", self.llm_service, llm_health_check)
        health_manager.add_service(
            "embedding", self.embedding_service, embedding_health_check
        )

        # Wait for health checks to complete
        time.sleep(0.2)

        # Check health status
        llm_health = health_manager.get_health_status("llm")
        embedding_health = health_manager.get_health_status("embedding")

        assert llm_health.is_healthy()
        assert embedding_health.is_healthy()
        assert "LLM healthy" in llm_health.message
        assert "Embedding healthy" in embedding_health.message

        # Clean up
        health_manager.stop_sync()


@pytest.mark.integration
class TestServiceChaining(IntegrationTestCase):
    """Integration tests for chaining service calls."""

    def setup_method(self):
        """Setup for service chaining tests."""
        super().setup_method()
        self._setup_test_services()

    def _setup_test_services(self):
        """Setup services for chaining tests."""
        self.llm_service = MockLLMService(
            responses=["Generated text for embedding"], delay=0.01
        )
        self.embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.01)
        self.vdb_service = MockVectorDBService(delay=0.01)

        self.service_manager = MockServiceManager()
        self.service_manager.add_mock_service("llm", self.llm_service)
        self.service_manager.add_mock_service("embedding", self.embedding_service)
        self.service_manager.add_mock_service("vdb", self.vdb_service)

        dependency_registry.register(BaseServiceManager, self.service_manager)

    def test_llm_to_embedding_chain(self):
        """Test chaining LLM output to embedding service."""
        # Step 1: Generate text with LLM
        llm_request = LLMServiceRequest(
            service_name="llm",
            operation_name="chat",
            raw_data={
                "messages": [{"role": "user", "content": "Generate a short text"}],
                "model": "test-model",
            },
        )

        llm = self.service_manager.get_service("llm")
        llm_response = llm.serve(llm_request)
        generated_text = llm_response.raw_data["response"].choices[0].message.content

        # Step 2: Embed the generated text
        embedding_request = EmbeddingServiceRequest(
            service_name="embedding",
            operation_name="embed",
            raw_data={"text": generated_text, "model": "test-embedding-model"},
        )

        embedding = self.service_manager.get_service("embedding")
        embedding_response = embedding.serve(embedding_request)

        # Verify the chain
        assert generated_text == "Generated text for embedding"
        assert len(embedding_response.raw_data["embeddings"]) == 1
        assert len(embedding_response.raw_data["embeddings"][0]) == 384

        # Verify both services were called
        assert self.llm_service.call_count == 1
        assert self.embedding_service.call_count == 1

    @pytest.mark.asyncio
    async def test_async_service_chain(self):
        """Test async chaining of services."""
        # Step 1: Generate text with LLM async
        llm_request = LLMServiceRequest(
            service_name="llm",
            operation_name="chat",
            raw_data={
                "messages": [{"role": "user", "content": "Generate text"}],
                "model": "test-model",
            },
        )

        llm = self.service_manager.get_service("llm")
        llm_response = await llm.serve_async(llm_request)
        generated_text = llm_response.raw_data["response"].choices[0].message.content

        # Step 2: Embed the text async
        embedding_request = EmbeddingServiceRequest(
            service_name="embedding",
            operation_name="embed",
            raw_data={"text": generated_text, "model": "test-embedding-model"},
        )

        embedding = self.service_manager.get_service("embedding")
        embedding_response = await embedding.serve_async(embedding_request)

        # Step 3: Store in vector DB async
        vdb_request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="insert",
            raw_data={
                "collection_name": "test_collection",
                "vectors": embedding_response.raw_data["embeddings"],
            },
        )

        vdb = self.service_manager.get_service("vdb")
        vdb_response = await vdb.serve_async(vdb_request)

        # Verify the entire async chain
        assert vdb_response.raw_data["status"] == "success"
        assert self.llm_service.call_count == 1
        assert self.embedding_service.call_count == 1
        assert self.vdb_service.call_count == 1

    def test_parallel_service_calls(self, performance_tracker):
        """Test parallel calls to different services."""
        performance_tracker.start_timer("parallel_calls")

        # Create multiple requests
        llm_requests = [LLMServiceRequestFactory() for _ in range(3)]
        embedding_requests = [EmbeddingServiceRequestFactory() for _ in range(3)]

        llm = self.service_manager.get_service("llm")
        embedding = self.service_manager.get_service("embedding")

        # Process requests
        for llm_req, emb_req in zip(llm_requests, embedding_requests):
            llm.serve(llm_req)
            embedding.serve(emb_req)

        performance_tracker.end_timer("parallel_calls")

        # Verify all requests were processed
        assert self.llm_service.call_count == 3
        assert self.embedding_service.call_count == 3

        # Check performance
        performance_tracker.assert_under_threshold("parallel_calls", 0.5)
