"""
Factory classes for generating test data and mock objects.

This module uses factory_boy to create realistic test data for
legoAgent services and requests.
"""

import random
from typing import Any, Dict, List, Optional

import factory
from faker import Faker

from lego_agent.core.service.llm import LLMServiceRequest, LLMServiceResponse
from lego_agent.core.service.embedding import (
    EmbeddingServiceRequest,
    EmbeddingServiceResponse,
)
from lego_agent.core.service.vdb import VectorDBServiceRequest, VectorDBServiceResponse
from lego_agent.agents.anthropic.patterns.task.llm_task import (
    LLMTaskRequest,
    LLMTaskResponse,
)
from lego_agent.agents.anthropic.patterns.workflow.chaining import ChainWorkflowRequest
from lego_agent.agents.anthropic.patterns.workflow.paralleling import (
    ParallelWorkflowRequest,
)
from lego_agent.agents.anthropic.patterns.workflow.routing import RouteWorkflowRequest

fake = Faker()


class LLMServiceRequestFactory(factory.Factory):
    """Factory for creating LLM service requests."""

    class Meta:
        model = LLMServiceRequest

    service_name = "llm"
    operation_name = "chat"
    raw_data = factory.LazyFunction(
        lambda: {
            "messages": [{"role": "user", "content": fake.text(max_nb_chars=200)}],
            "model": fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"]),
        }
    )


class LLMTaskRequestFactory(factory.Factory):
    """Factory for creating LLM task requests."""

    class Meta:
        model = LLMTaskRequest

    prompt = factory.LazyFunction(lambda: fake.text(max_nb_chars=500))
    system_prompt = factory.LazyFunction(lambda: fake.sentence())
    model = factory.LazyFunction(
        lambda: fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
    )


class ChainWorkflowRequestFactory(factory.Factory):
    """Factory for creating chain workflow requests."""

    class Meta:
        model = ChainWorkflowRequest

    input = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    prompts = factory.LazyFunction(
        lambda: [fake.sentence() for _ in range(random.randint(2, 5))]
    )
    model = factory.LazyFunction(
        lambda: fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
    )


class ParallelWorkflowRequestFactory(factory.Factory):
    """Factory for creating parallel workflow requests."""

    class Meta:
        model = ParallelWorkflowRequest

    prompt = factory.LazyFunction(lambda: fake.sentence())
    inputs = factory.LazyFunction(
        lambda: [fake.text(max_nb_chars=100) for _ in range(random.randint(2, 8))]
    )
    n_workers = factory.LazyFunction(lambda: random.randint(2, 6))
    model = factory.LazyFunction(
        lambda: fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
    )


class RouteWorkflowRequestFactory(factory.Factory):
    """Factory for creating route workflow requests."""

    class Meta:
        model = RouteWorkflowRequest

    input = factory.LazyFunction(lambda: fake.text(max_nb_chars=200))
    routes = factory.LazyFunction(
        lambda: {fake.word(): fake.sentence() for _ in range(random.randint(2, 5))}
    )
    model = factory.LazyFunction(
        lambda: fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
    )
    show_reasoning = factory.LazyFunction(lambda: fake.boolean())


class EmbeddingServiceRequestFactory(factory.Factory):
    """Factory for creating embedding service requests."""

    class Meta:
        model = EmbeddingServiceRequest

    service_name = "embedding"
    operation_name = "embed"
    raw_data = factory.LazyFunction(
        lambda: {
            "texts": [fake.sentence() for _ in range(random.randint(1, 10))],
            "model": fake.random_element(
                ["text-embedding-ada-002", "sentence-transformers"]
            ),
        }
    )


class VectorDBServiceRequestFactory(factory.Factory):
    """Factory for creating vector DB service requests."""

    class Meta:
        model = VectorDBServiceRequest

    service_name = "vdb"
    operation_name = factory.LazyFunction(
        lambda: fake.random_element(["search", "insert", "create_collection"])
    )
    raw_data = factory.LazyFunction(
        lambda: {
            "collection_name": fake.word(),
            "query_embeddings": [[random.random() for _ in range(384)]],
            "k": random.randint(1, 10),
        }
    )


# Response factories


class LLMResponseFactory(factory.Factory):
    """Factory for creating mock LLM responses."""

    class Meta:
        model = dict

    choices = factory.LazyFunction(
        lambda: [
            {
                "message": {
                    "content": fake.text(max_nb_chars=1000),
                    "role": "assistant",
                },
                "finish_reason": "stop",
            }
        ]
    )
    model = factory.LazyFunction(
        lambda: fake.random_element(["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"])
    )
    usage = factory.LazyFunction(
        lambda: {
            "prompt_tokens": random.randint(10, 100),
            "completion_tokens": random.randint(50, 500),
            "total_tokens": random.randint(60, 600),
        }
    )


class EmbeddingResponseFactory(factory.Factory):
    """Factory for creating mock embedding responses."""

    class Meta:
        model = list

    @classmethod
    def create(
        cls, num_embeddings: int = 1, embedding_dim: int = 384
    ) -> List[List[float]]:
        """Create a list of mock embeddings."""
        return [
            [random.random() for _ in range(embedding_dim)]
            for _ in range(num_embeddings)
        ]


class VDBResponseFactory(factory.Factory):
    """Factory for creating mock vector DB responses."""

    class Meta:
        model = list

    @classmethod
    def create_search_results(
        cls, num_queries: int = 1, results_per_query: int = 5
    ) -> List[List[Dict[str, Any]]]:
        """Create mock search results."""
        results = []
        for query_idx in range(num_queries):
            query_results = []
            for result_idx in range(results_per_query):
                query_results.append(
                    {
                        "id": f"doc_{query_idx}_{result_idx}",
                        "distance": random.random(),
                        "metadata": {
                            "text": fake.text(max_nb_chars=200),
                            "category": fake.word(),
                            "timestamp": fake.date_time().isoformat(),
                        },
                    }
                )
            results.append(query_results)
        return results


# Utility functions for creating test data


def create_test_messages(num_messages: int = 3) -> List[Dict[str, str]]:
    """Create a list of test chat messages."""
    messages = []
    roles = ["user", "assistant"]

    for i in range(num_messages):
        role = roles[i % 2]
        content = fake.text(max_nb_chars=200)
        messages.append({"role": role, "content": content})

    return messages


def create_test_embeddings(
    num_embeddings: int = 5, embedding_dim: int = 384
) -> List[List[float]]:
    """Create a list of test embeddings."""
    return EmbeddingResponseFactory.create(num_embeddings, embedding_dim)


def create_test_documents(num_docs: int = 10) -> List[Dict[str, Any]]:
    """Create a list of test documents for vector DB operations."""
    documents = []
    for i in range(num_docs):
        documents.append(
            {
                "id": f"doc_{i}",
                "text": fake.text(max_nb_chars=500),
                "metadata": {
                    "title": fake.sentence(nb_words=4),
                    "category": fake.word(),
                    "author": fake.name(),
                    "created_at": fake.date_time().isoformat(),
                },
                "embedding": [random.random() for _ in range(384)],
            }
        )
    return documents


# Service-specific factory collections


class ServiceRequestFactory:
    """Collection of factories for creating service requests."""

    llm = LLMServiceRequestFactory
    embedding = EmbeddingServiceRequestFactory
    vdb = VectorDBServiceRequestFactory


class WorkflowRequestFactory:
    """Collection of factories for creating workflow requests."""

    chain = ChainWorkflowRequestFactory
    parallel = ParallelWorkflowRequestFactory
    route = RouteWorkflowRequestFactory
    llm_task = LLMTaskRequestFactory


class ResponseFactory:
    """Collection of factories for creating mock responses."""

    llm = LLMResponseFactory
    embedding = EmbeddingResponseFactory
    vdb = VDBResponseFactory
