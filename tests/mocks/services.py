"""
Mock service implementations for testing.

This module provides mock implementations of all legoAgent services,
allowing for isolated testing without external dependencies.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Union
from unittest.mock import MagicMock, AsyncMock

from lego_agent.core.service.base import (
    BaseService,
    BaseServiceRequest,
    BaseServiceResponse,
)
from lego_agent.core.service.llm import (
    LLMService,
    LLMServiceRequest,
    LLMServiceResponse,
)
from lego_agent.core.service.vdb import (
    VectorDBService,
    VectorDBServiceRequest,
    VectorDBServiceResponse,
)
from lego_agent.core.service.embedding import (
    EmbeddingService,
    EmbeddingServiceRequest,
    EmbeddingServiceResponse,
)
from lego_agent.core.services_manager import BaseServiceManager


class MockLLMService(LLMService):
    """Mock LLM service for testing."""

    def __init__(self, responses: Optional[List[str]] = None, delay: float = 0.0):
        super().__init__()
        self.responses = responses or ["Mock LLM response"]
        self.delay = delay
        self.call_count = 0
        self.last_request = None
        self.call_history = []

    def serve(
        self, request: LLMServiceRequest, model: str = None
    ) -> LLMServiceResponse:
        """Serve a mock LLM request."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            time.sleep(self.delay)

        response_text = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1

        # Create a mock response object that mimics OpenAI's structure
        mock_choice = MagicMock()
        mock_choice.message.content = response_text

        mock_response = MagicMock()
        mock_response.choices = [mock_choice]

        return LLMServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=request.operation_name,
            raw_data={"response": mock_response},
        )

    async def serve_async(
        self, request: LLMServiceRequest, model: str = None
    ) -> LLMServiceResponse:
        """Serve a mock LLM request asynchronously."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            await asyncio.sleep(self.delay)

        response_text = self.responses[self.call_count % len(self.responses)]
        self.call_count += 1

        # Create a mock response object that mimics OpenAI's structure
        mock_choice = MagicMock()
        mock_choice.message.content = response_text

        mock_response = MagicMock()
        mock_response.choices = [mock_choice]

        return LLMServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=request.operation_name,
            raw_data={"response": mock_response},
        )

    def reset(self):
        """Reset the mock service state."""
        self.call_count = 0
        self.last_request = None
        self.call_history = []


class MockEmbeddingService(EmbeddingService):
    """Mock embedding service for testing."""

    def __init__(self, embedding_dim: int = 384, delay: float = 0.0):
        super().__init__()
        self.embedding_dim = embedding_dim
        self.delay = delay
        self.call_count = 0
        self.last_request = None
        self.call_history = []

    def serve(
        self, request: EmbeddingServiceRequest, model: str = None
    ) -> EmbeddingServiceResponse:
        """Serve a mock embedding request."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            time.sleep(self.delay)

        # Generate mock embeddings based on input
        raw_data = request.raw_data
        if "texts" in raw_data:
            texts = raw_data["texts"]
            embeddings = []
            for i, text in enumerate(texts):
                # Create deterministic embeddings based on text hash and index
                embedding = [
                    (hash(text) + i + j) % 100 / 100.0
                    for j in range(self.embedding_dim)
                ]
                embeddings.append(embedding)
        else:
            # Single text embedding
            text = raw_data.get("text", "default")
            embedding = [
                (hash(text) + j) % 100 / 100.0 for j in range(self.embedding_dim)
            ]
            embeddings = [embedding]

        self.call_count += 1

        return EmbeddingServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=request.operation_name,
            raw_data={"embeddings": embeddings},
        )

    async def serve_async(
        self, request: EmbeddingServiceRequest, model: str = None
    ) -> EmbeddingServiceResponse:
        """Serve a mock embedding request asynchronously."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            await asyncio.sleep(self.delay)

        # Generate mock embeddings (same logic as sync version)
        raw_data = request.raw_data
        if "texts" in raw_data:
            texts = raw_data["texts"]
            embeddings = []
            for i, text in enumerate(texts):
                embedding = [
                    (hash(text) + i + j) % 100 / 100.0
                    for j in range(self.embedding_dim)
                ]
                embeddings.append(embedding)
        else:
            text = raw_data.get("text", "default")
            embedding = [
                (hash(text) + j) % 100 / 100.0 for j in range(self.embedding_dim)
            ]
            embeddings = [embedding]

        self.call_count += 1

        return EmbeddingServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=request.operation_name,
            raw_data={"embeddings": embeddings},
        )

    def reset(self):
        """Reset the mock service state."""
        self.call_count = 0
        self.last_request = None
        self.call_history = []


class MockVectorDBService(VectorDBService):
    """Mock vector database service for testing."""

    def __init__(self, delay: float = 0.0):
        super().__init__()
        self.delay = delay
        self.call_count = 0
        self.last_request = None
        self.call_history = []
        self.stored_vectors = {}  # collection_name -> list of vectors

    def serve(
        self, request: VectorDBServiceRequest, model: str = None
    ) -> VectorDBServiceResponse:
        """Serve a mock vector DB request."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            time.sleep(self.delay)

        operation = request.operation_name
        raw_data = request.raw_data

        if operation == "search":
            # Mock search results
            results = self._mock_search_results(raw_data)
        elif operation == "insert":
            # Mock insert operation
            results = self._mock_insert_results(raw_data)
        elif operation == "create_collection":
            # Mock collection creation
            results = {
                "status": "success",
                "collection_name": raw_data.get("collection_name"),
            }
        else:
            results = {"status": "success", "operation": operation}

        self.call_count += 1

        return VectorDBServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=operation,
            raw_data=results,
        )

    async def serve_async(
        self, request: VectorDBServiceRequest, model: str = None
    ) -> VectorDBServiceResponse:
        """Serve a mock vector DB request asynchronously."""
        self.last_request = request
        self.call_history.append(request)

        if self.delay > 0:
            await asyncio.sleep(self.delay)

        operation = request.operation_name
        raw_data = request.raw_data

        if operation == "search":
            results = self._mock_search_results(raw_data)
        elif operation == "insert":
            results = self._mock_insert_results(raw_data)
        elif operation == "create_collection":
            results = {
                "status": "success",
                "collection_name": raw_data.get("collection_name"),
            }
        else:
            results = {"status": "success", "operation": operation}

        self.call_count += 1

        return VectorDBServiceResponse(
            service_name=self.service_name,
            instance_name="mock_instance",
            operation_name=operation,
            raw_data=results,
        )

    def _mock_search_results(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock search results."""
        k = raw_data.get("k", 5)
        query_embeddings = raw_data.get("query_embeddings", [[0.1] * 384])

        results = []
        for query_idx, query_embedding in enumerate(query_embeddings):
            query_results = []
            for i in range(min(k, 3)):  # Return up to 3 mock results per query
                query_results.append(
                    {
                        "id": f"doc_{query_idx}_{i}",
                        "distance": 0.1 + i * 0.05,  # Mock distances
                        "metadata": {
                            "text": f"Mock document {i} for query {query_idx}"
                        },
                    }
                )
            results.append(query_results)

        return {"results": results}

    def _mock_insert_results(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate mock insert results."""
        vectors = raw_data.get("vectors", [])
        return {
            "status": "success",
            "inserted_count": len(vectors),
            "ids": [f"id_{i}" for i in range(len(vectors))],
        }

    def reset(self):
        """Reset the mock service state."""
        self.call_count = 0
        self.last_request = None
        self.call_history = []
        self.stored_vectors = {}


class MockServiceManager(BaseServiceManager):
    """Mock service manager for testing."""

    def __init__(self):
        super().__init__()
        self.mock_services = {}

    def add_mock_service(
        self, service_name: str, mock_service: BaseService, health_check=None
    ):
        """Add a mock service to the manager."""
        self.mock_services[service_name] = mock_service
        self.add_service(service_name, mock_service, health_check)

    def get_service(self, service_name: str) -> BaseService:
        """Get a service by name."""
        if service_name in self.mock_services:
            return self.mock_services[service_name]
        return super().get_service(service_name)

    def reset_all_mocks(self):
        """Reset all mock services."""
        for service in self.mock_services.values():
            if hasattr(service, "reset"):
                service.reset()


# Convenience functions for creating mock responses


def create_mock_llm_response(
    content: str, model: str = "mock-model"
) -> LLMServiceResponse:
    """Create a mock LLM response."""
    mock_choice = MagicMock()
    mock_choice.message.content = content

    mock_response = MagicMock()
    mock_response.choices = [mock_choice]
    mock_response.model = model

    return LLMServiceResponse(
        service_name="llm",
        instance_name="mock_instance",
        operation_name="chat",
        raw_data={"response": mock_response},
    )


def create_mock_embedding_response(
    embeddings: List[List[float]],
) -> EmbeddingServiceResponse:
    """Create a mock embedding response."""
    return EmbeddingServiceResponse(
        service_name="embedding",
        instance_name="mock_instance",
        operation_name="embed",
        raw_data={"embeddings": embeddings},
    )


def create_mock_vdb_response(
    results: List[List[Dict[str, Any]]],
) -> VectorDBServiceResponse:
    """Create a mock vector DB response."""
    return VectorDBServiceResponse(
        service_name="vdb",
        instance_name="mock_instance",
        operation_name="search",
        raw_data={"results": results},
    )
