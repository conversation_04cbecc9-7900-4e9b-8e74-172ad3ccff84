"""Tests for the configuration management system."""

import pytest
import os
from pathlib import Path
import yaml
from pydantic import BaseModel
from lego_agent.core.config import Configuration<PERSON>ana<PERSON>, ConfigurationError


class SampleConfig(BaseModel):
    name: str
    value: int
    nested: dict


def test_config_manager_init():
    """Test configuration manager initialization."""
    config_dir = ".test_config"
    os.makedirs(config_dir, exist_ok=True)

    manager = ConfigurationManager(config_dir)
    assert manager.config_dir == config_dir
    assert manager.configs == {}


def test_load_config(tmp_path):
    """Test loading configuration from file."""
    config_dir = tmp_path / "config"
    config_dir.mkdir()

    # Create a test config file
    config_data = {"test": {"name": "test", "value": 42}}
    config_file = config_dir / "test.yaml"
    with open(config_file, "w") as f:
        yaml.dump(config_data, f)

    manager = ConfigurationManager(str(config_dir))
    config = manager.load_config("test")

    assert config == config_data
    assert manager.configs["test"] == config_data


def test_get_config_value(tmp_path):
    """Test getting specific configuration values."""
    config_dir = tmp_path / "config"
    config_dir.mkdir()

    # Create a test config file
    config_data = {
        "app": {
            "name": "lego-agent",
            "version": "0.1.0",
            "settings": {"debug": True, "timeout": 30},
        }
    }
    config_file = config_dir / "app.yaml"
    with open(config_file, "w") as f:
        yaml.dump(config_data, f)

    manager = ConfigurationManager(str(config_dir))

    # Test getting values with dot notation
    assert manager.get_config_value("app", "app.name") == "lego-agent"
    assert manager.get_config_value("app", "app.settings.debug") is True
    assert manager.get_config_value("app", "app.settings.timeout") == 30

    # Test default values
    assert manager.get_config_value("app", "app.nonexistent", "default") == "default"


def test_get_typed_config(tmp_path):
    """Test getting typed configuration using Pydantic models."""
    config_dir = tmp_path / "config"
    config_dir.mkdir()

    # Create a test config file
    config_data = {"name": "test-config", "value": 42, "nested": {"key": "value"}}
    config_file = config_dir / "typed.yaml"
    with open(config_file, "w") as f:
        yaml.dump(config_data, f)

    manager = ConfigurationManager(str(config_dir))

    # Get typed config
    typed_config = manager.get_typed_config("typed", SampleConfig)

    assert isinstance(typed_config, SampleConfig)
    assert typed_config.name == "test-config"
    assert typed_config.value == 42
    assert typed_config.nested == {"key": "value"}


def test_config_not_found():
    """Test error handling for missing configuration files."""
    manager = ConfigurationManager("nonexistent_dir")

    with pytest.raises(ConfigurationError):
        manager.load_config("nonexistent")
