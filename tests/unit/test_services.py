"""
Unit tests for core services using the new testing framework.

This module demonstrates the new testing approach for individual service components.
"""

import pytest
from unittest.mock import MagicMock, patch

from tests.base import UnitTestCase
from tests.mocks.services import (
    MockLLMService,
    MockEmbeddingService,
    MockVectorDBService,
)
from tests.mocks.factories import (
    LLMServiceRequestFactory,
    EmbeddingServiceRequestFactory,
)

from lego_agent.core.service.llm import LLMServiceRequest, LLMServiceResponse
from lego_agent.core.service.embedding import (
    EmbeddingServiceRequest,
    EmbeddingServiceResponse,
)
from lego_agent.core.service.vdb import VectorDBServiceRequest, VectorDBServiceResponse


@pytest.mark.unit
class TestLLMService(UnitTestCase):
    """Unit tests for LLM service."""

    def setup_method(self):
        """Setup for LLM service tests."""
        super().setup_method()
        self.llm_service = MockLLMService(
            responses=["Test response 1", "Test response 2"], delay=0.0
        )

    def test_llm_service_basic_request(self):
        """Test basic LLM service request."""
        request = LLMServiceRequestFactory()
        response = self.llm_service.serve(request)

        assert isinstance(response, LLMServiceResponse)
        assert response.service_name == "llm"
        assert response.instance_name == "mock_instance"
        assert "response" in response.raw_data

        # Check that the service tracked the request
        assert self.llm_service.call_count == 1
        assert self.llm_service.last_request == request

    def test_llm_service_multiple_requests(self):
        """Test multiple requests to LLM service."""
        requests = [LLMServiceRequestFactory() for _ in range(3)]
        responses = []

        for request in requests:
            response = self.llm_service.serve(request)
            responses.append(response)

        assert len(responses) == 3
        assert self.llm_service.call_count == 3
        assert len(self.llm_service.call_history) == 3

        # Verify responses cycle through predefined responses
        for i, response in enumerate(responses):
            expected_content = self.llm_service.responses[
                i % len(self.llm_service.responses)
            ]
            actual_content = response.raw_data["response"].choices[0].message.content
            assert actual_content == expected_content

    @pytest.mark.asyncio
    async def test_llm_service_async_request(self):
        """Test async LLM service request."""
        request = LLMServiceRequestFactory()
        response = await self.llm_service.serve_async(request)

        assert isinstance(response, LLMServiceResponse)
        assert response.service_name == "llm"
        assert self.llm_service.call_count == 1

    def test_llm_service_reset(self):
        """Test LLM service reset functionality."""
        request = LLMServiceRequestFactory()
        self.llm_service.serve(request)

        assert self.llm_service.call_count == 1
        assert self.llm_service.last_request is not None

        self.llm_service.reset()

        assert self.llm_service.call_count == 0
        assert self.llm_service.last_request is None
        assert len(self.llm_service.call_history) == 0

    def test_llm_service_performance(self, performance_tracker):
        """Test LLM service performance."""
        request = LLMServiceRequestFactory()

        performance_tracker.start_timer("llm_request")
        response = self.llm_service.serve(request)
        performance_tracker.end_timer("llm_request")

        assert response is not None
        performance_tracker.assert_under_threshold("llm_request", 0.1)


@pytest.mark.unit
class TestEmbeddingService(UnitTestCase):
    """Unit tests for Embedding service."""

    def setup_method(self):
        """Setup for embedding service tests."""
        super().setup_method()
        self.embedding_service = MockEmbeddingService(embedding_dim=384, delay=0.0)

    def test_embedding_service_single_text(self):
        """Test embedding service with single text."""
        request = EmbeddingServiceRequest(
            service_name="embedding",
            operation_name="embed",
            raw_data={"text": "Test text", "model": "test-model"},
        )

        response = self.embedding_service.serve(request)

        assert isinstance(response, EmbeddingServiceResponse)
        assert response.service_name == "embedding"
        assert "embeddings" in response.raw_data

        embeddings = response.raw_data["embeddings"]
        assert len(embeddings) == 1
        assert len(embeddings[0]) == 384

    def test_embedding_service_multiple_texts(self):
        """Test embedding service with multiple texts."""
        texts = ["Text 1", "Text 2", "Text 3"]
        request = EmbeddingServiceRequest(
            service_name="embedding",
            operation_name="embed",
            raw_data={"texts": texts, "model": "test-model"},
        )

        response = self.embedding_service.serve(request)
        embeddings = response.raw_data["embeddings"]

        assert len(embeddings) == len(texts)
        for embedding in embeddings:
            assert len(embedding) == 384
            assert all(isinstance(val, float) for val in embedding)

    @pytest.mark.asyncio
    async def test_embedding_service_async(self):
        """Test async embedding service."""
        request = EmbeddingServiceRequestFactory()
        response = await self.embedding_service.serve_async(request)

        assert isinstance(response, EmbeddingServiceResponse)
        assert self.embedding_service.call_count == 1

    def test_embedding_deterministic(self):
        """Test that embeddings are deterministic for same input."""
        text = "Consistent test text"
        request = EmbeddingServiceRequest(
            service_name="embedding",
            operation_name="embed",
            raw_data={"text": text, "model": "test-model"},
        )

        response1 = self.embedding_service.serve(request)
        self.embedding_service.reset()
        response2 = self.embedding_service.serve(request)

        embeddings1 = response1.raw_data["embeddings"][0]
        embeddings2 = response2.raw_data["embeddings"][0]

        assert embeddings1 == embeddings2


@pytest.mark.unit
class TestVectorDBService(UnitTestCase):
    """Unit tests for Vector Database service."""

    def setup_method(self):
        """Setup for vector DB service tests."""
        super().setup_method()
        self.vdb_service = MockVectorDBService(delay=0.0)

    def test_vdb_search_operation(self):
        """Test vector DB search operation."""
        request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="search",
            raw_data={
                "collection_name": "test_collection",
                "query_embeddings": [[0.1] * 384],
                "k": 5,
            },
        )

        response = self.vdb_service.serve(request)

        assert isinstance(response, VectorDBServiceResponse)
        assert response.operation_name == "search"
        assert "results" in response.raw_data

        results = response.raw_data["results"]
        assert len(results) == 1  # One query
        assert len(results[0]) <= 5  # Up to k results

    def test_vdb_insert_operation(self):
        """Test vector DB insert operation."""
        vectors = [[0.1] * 384, [0.2] * 384, [0.3] * 384]
        request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="insert",
            raw_data={"collection_name": "test_collection", "vectors": vectors},
        )

        response = self.vdb_service.serve(request)

        assert response.operation_name == "insert"
        assert response.raw_data["status"] == "success"
        assert response.raw_data["inserted_count"] == len(vectors)
        assert len(response.raw_data["ids"]) == len(vectors)

    def test_vdb_create_collection(self):
        """Test vector DB collection creation."""
        request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="create_collection",
            raw_data={"collection_name": "new_collection"},
        )

        response = self.vdb_service.serve(request)

        assert response.operation_name == "create_collection"
        assert response.raw_data["status"] == "success"
        assert response.raw_data["collection_name"] == "new_collection"

    @pytest.mark.asyncio
    async def test_vdb_async_operations(self):
        """Test async vector DB operations."""
        search_request = VectorDBServiceRequest(
            service_name="vdb",
            operation_name="search",
            raw_data={"query_embeddings": [[0.1] * 384], "k": 3},
        )

        response = await self.vdb_service.serve_async(search_request)

        assert isinstance(response, VectorDBServiceResponse)
        assert self.vdb_service.call_count == 1

    def test_vdb_call_history(self):
        """Test that VDB service maintains call history."""
        requests = [
            VectorDBServiceRequest(
                service_name="vdb",
                operation_name="search",
                raw_data={"query_embeddings": [[0.1] * 384], "k": 1},
            ),
            VectorDBServiceRequest(
                service_name="vdb",
                operation_name="insert",
                raw_data={"vectors": [[0.2] * 384]},
            ),
        ]

        for request in requests:
            self.vdb_service.serve(request)

        assert len(self.vdb_service.call_history) == 2
        assert self.vdb_service.call_history[0].operation_name == "search"
        assert self.vdb_service.call_history[1].operation_name == "insert"


@pytest.mark.unit
class TestServiceIntegration(UnitTestCase):
    """Unit tests for service integration scenarios."""

    def test_service_error_handling(self):
        """Test service error handling."""
        service = MockLLMService(responses=["Test"], delay=0.0)

        # Test with None request (should raise AttributeError)
        with pytest.raises(AttributeError):
            service.serve(None)

    def test_service_performance_under_load(self, performance_tracker):
        """Test service performance under load."""
        service = MockLLMService(responses=["Response"] * 100, delay=0.001)

        performance_tracker.start_timer("bulk_requests")

        for i in range(50):
            request = LLMServiceRequestFactory()
            response = service.serve(request)
            assert response is not None

        performance_tracker.end_timer("bulk_requests")
        performance_tracker.assert_under_threshold("bulk_requests", 1.0)

        assert service.call_count == 50
